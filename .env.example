# Phantom Billing Service Configuration
# Copy this file to .env and fill in your actual values

NODE_ENV = production

# MagnusBilling API Configuration
API_URL=http://5.187.2.47/mbilling/index.php/call/read
API_KEY=your_magnus_api_key_here
API_SECRET=your_magnus_api_secret_here
MAX_RETRIES=3
RETRY_BASE_DELAY=10000

# Supabase Configuration
SUPABASE_URL=https://your-project.supabase.co
SUPABASE_SERVICE_KEY=your_supabase_service_role_key_here

# Processing Configuration
POLL_INTERVAL=300000        # 5 minutes in milliseconds
BATCH_SIZE=1000            # Number of CDRs to fetch per API call
ROTATION_MONTHS=3          # How many months to keep charges before cleanup
CLEANUP_INTERVAL=86400000  # 24 hours in milliseconds
DEFAULT_RATE=0.01          # Default rate per second for charge calculation

# Database Configuration
STATE_PATH=state.json      # Local JSON file path for state management

# Logging Configuration
LOG_LEVEL=info             # error, warn, info, debug
METRICS_INTERVAL=60000     # Metrics logging interval in milliseconds (1 minute)

# Optional Performance Tuning
# Increase if you have high CDR volume
# MAX_CONCURRENT_BATCHES=5
# CONNECTION_POOL_SIZE=10
