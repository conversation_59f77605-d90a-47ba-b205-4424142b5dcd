# Phantom Billing Service

A high-performance microservice for processing Call Detail Records (CDRs) from MagnusBilling API and managing billing charges in Supabase PostgreSQL database.

## Overview

This service periodically polls the MagnusBilling API for CDR data, processes charges sequentially to ensure idempotency, and stores billing information in a Supabase-hosted PostgreSQL database. It uses DuckDB for local state management and is designed for minimal latency and high throughput.

## Architecture

- **Runtime**: Bun for fast JavaScript execution
- **Language**: Raw JavaScript (no TypeScript)
- **API Source**: MagnusBilling API with HMAC-SHA512 authentication
- **Remote Database**: Supabase PostgreSQL for billing data
- **Local Database**: DuckDB in-memory database for high-performance state management
- **Deployment**: Direct host machine execution (no Docker)

## Features

- ✅ Sequential CDR processing with idempotency guarantees
- ✅ HMAC-SHA512 authentication for MagnusBilling API
- ✅ Automatic deduplication using CDR IDs
- ✅ Batch processing for high throughput
- ✅ Exponential backoff retry logic
- ✅ Automatic cleanup of old charges (configurable retention period)
- ✅ Comprehensive logging and metrics
- ✅ Graceful shutdown handling
- ✅ Local state management with DuckDB

## Prerequisites

- **Bun** runtime (v1.0+)
- **PM2** process manager
- **MagnusBilling** API access with credentials
- **Supabase** project with PostgreSQL database

## Installation

### 1. Install Bun and PM2

```bash
# Install Bun
curl -fsSL https://bun.sh/install | bash
source ~/.bashrc

# Install PM2
npm install -g pm2
```

### 2. Clone and Setup Project

```bash
# Clone the repository
git clone <repository-url>
cd phantom-billing-service

# Install dependencies
bun install

# Copy environment configuration
cp .env.example .env
```

### 3. Configure Environment Variables

Edit `.env` with your actual credentials:

```bash
# MagnusBilling API
API_KEY=your_actual_api_key
API_SECRET=your_actual_api_secret

# Supabase
SUPABASE_URL=https://your-project.supabase.co
SUPABASE_SERVICE_KEY=your_service_role_key

# Optional: Adjust processing parameters
POLL_INTERVAL=300000      # 5 minutes
BATCH_SIZE=1000
ROTATION_MONTHS=3
DEFAULT_RATE=0.01
```

### 4. Setup Supabase Database

Run the SQL setup script in your Supabase SQL editor:

```bash
# Copy the contents of sql/supabase_setup.sql
# and execute it in your Supabase project
cat sql/supabase_setup.sql
```

This creates:
- `call_charges` table for billing records with profit tracking
- Proper indexes for efficient queries
- Database permissions for the service

## Usage

### Development Mode

```bash
# Quick development setup with hot reloading
./dev.sh

# Or manually:
bun run dev                    # Run with debug logging
bun run pm2:start:dev         # Start with PM2 (hot reload)
bun run pm2:logs:dev          # View development logs

# Test configuration
bun run test-config
```

### Production Build & Deployment

```bash
# Quick production deployment
./deploy.sh

# Or step by step:
bun run build:prod           # Build optimized bundle
bun run prod                 # Test built version
bun run pm2:start           # Deploy with PM2

# Build commands
bun run build               # Development build with sourcemaps + config copy
bun run build:prod          # Production build (optimized)
bun run clean               # Clean build artifacts
```

### PM2 Process Management

```bash
# Production service
npm run pm2:start           # Start production service
npm run pm2:stop            # Stop production service
npm run pm2:restart         # Restart production service
npm run pm2:logs            # View production logs

# Development service
npm run pm2:start:dev       # Start development service
npm run pm2:stop:dev        # Stop development service
npm run pm2:logs:dev        # View development logs

# General PM2 commands
pm2 status                  # View all services
pm2 monit                   # Real-time monitoring
pm2 startup                 # Enable startup script
pm2 save                    # Save current processes
```

## Configuration

### Environment Variables

| Variable | Default | Description |
|----------|---------|-------------|
| `API_KEY` | - | MagnusBilling API key (required) |
| `API_SECRET` | - | MagnusBilling API secret (required) |
| `SUPABASE_URL` | - | Supabase project URL (required) |
| `SUPABASE_SERVICE_KEY` | - | Supabase service role key (required) |
| `POLL_INTERVAL` | 300000 | Polling interval in milliseconds |
| `BATCH_SIZE` | 1000 | CDRs to fetch per API call |
| `ROTATION_MONTHS` | 3 | Months to keep charges before cleanup (both local and Supabase) |
| `DEFAULT_RATE` | 0.01 | Default rate per second |
| `CLEANUP_INTERVAL` | 86400000 | Cleanup interval in milliseconds (24 hours) |
| `LOG_LEVEL` | info | Logging level (error/warn/info/debug) |

### Rate Configuration

The service supports different charging models:

1. **Fixed Rate**: Set `DEFAULT_RATE` for all users
2. **Per-User Rates**: Implement custom logic in `magnus_client.js`
3. **Plan-Based Rates**: Extend to use plan information from CDR data

### Build System

The service uses Bun's native build system for production optimization:

**Development Build:**
- Includes sourcemaps for debugging
- No minification for readable stack traces
- Faster build times

**Production Build:**
- Minified and optimized code
- External dependencies excluded (runtime resolution)
- Code splitting for better performance
- ~94% smaller bundle size (30KB vs 520KB)

**Build Outputs:**
- `build/cdr_processor.js` - Main application bundle
- `build/cdr_processor.js.map` - Source map (development only)

**Recent Optimizations:**
- Removed 200+ lines of unused code and duplicate methods
- Cleaned up redundant files and simplified project structure
- Optimized bundle size from 31KB to 30KB

### Data Retention and Cleanup

The service automatically manages data retention using the `ROTATION_MONTHS` configuration:

- **Local Storage (DuckDB)**: Keeps CDR data for `ROTATION_MONTHS` for fast local processing and deduplication
- **Remote Storage (Supabase)**: Cleans up charges older than `ROTATION_MONTHS` to manage database size
- **Cleanup Schedule**: Runs every 24 hours (configurable via `CLEANUP_INTERVAL`)
- **Safety**: Only deletes local records that have been successfully synced to Supabase

**Cleanup Process:**
1. **DuckDB Cleanup**: Removes CDRs older than `ROTATION_MONTHS` that are marked as `synced_to_supabase = 1`
2. **Supabase Cleanup**: Removes charges older than `ROTATION_MONTHS` based on `start_time`
3. **Preservation**: Unsynced local data is never deleted to prevent data loss

## Monitoring

### Logs

The service logs structured JSON to stdout/stderr:

```bash
# View real-time logs
tail -f /var/log/phantom-billing/service.log

# Search for errors
grep -i error /var/log/phantom-billing/service.log

# View metrics
grep "Processing metrics" /var/log/phantom-billing/service.log
```

### Metrics

Key metrics logged every minute:
- CDRs fetched and processed
- Processing times
- Error counts
- API call statistics
- Service uptime

### Health Checks

```bash
# Check if service is running
pm2 status phantom-billing-service

# Check recent processing
pm2 logs phantom-billing-service --lines 20

# Check for errors
pm2 logs phantom-billing-service --err

# Use built-in monitor
bun monitor.js
```

## Database Schema

### Supabase Tables

#### call_charges
```sql
CREATE TABLE call_charges (
    cdr_id TEXT PRIMARY KEY,
    sip_user TEXT NOT NULL,
    duration FLOAT NOT NULL,
    charge FLOAT NOT NULL,
    cost NUMERIC DEFAULT 0,
    processed BOOLEAN DEFAULT false,
    start_time TIMESTAMP NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

### Local State (DuckDB)
- `application_state` table with key-value pairs for state management
- In-memory database for maximum performance
- ACID transactions for data integrity

## API Integration

### MagnusBilling API

- **Endpoint**: `http://**********/mbilling/index.php/call/read`
- **Method**: POST
- **Authentication**: HMAC-SHA512 signature
- **Parameters**:
  - `action=read`
  - `module=Call`
  - `order=starttime+DESC`
  - `limit=1000`
  - `since=<unix_timestamp>`
  - `nonce=<current_timestamp>`

### CDR Response Format

Example CDR record from MagnusBilling API:

```json
{
  "id": "42",
  "id_user": "3",
  "id_plan": "1",
  "id_trunk": null,
  "id_server": null,
  "id_prefix": null,
  "id_campaign": null,
  "callerid": "45176",
  "uniqueid": "1755694723.102",
  "starttime": "2025-08-20 14:58:44",
  "sessiontime": "62",
  "calledstation": "34624",
  "sessionbill": "0",
  "sipiax": "1",
  "src": "45176",
  "buycost": "0.000000",
  "real_sessiontime": "0",
  "terminatecauseid": "1",
  "agent_bill": "0.000000",
  "idUserusername": "gen_test",
  "idPlanname": "plan_test",
  "idTrunktrunkcode": null,
  "idPrefixdestination": null,
  "idCampaignname": null,
  "idServername": null
}
```

**Key Fields:**
- `id`: Unique CDR identifier (used as primary key)
- `starttime`: Call start timestamp
- `sessiontime`: Call duration in seconds
- `callerid`: Caller ID number
- `calledstation`: Destination number
- `idUserusername`: SIP user account
- `sessionbill`: Calculated charge amount
- `buycost`: Provider cost for the call

### Authentication

```javascript
const signature = crypto.createHmac('sha512', apiSecret)
    .update(postData)
    .digest('hex');
```

## Troubleshooting

### Common Issues

1. **Connection Errors**
   ```bash
   # Check network connectivity
   curl -I http://**********/mbilling/index.php/call/read
   
   # Test Supabase connection
   curl -H "apikey: your_key" https://your-project.supabase.co/rest/v1/
   ```

2. **Authentication Failures**
   - Verify API_KEY and API_SECRET are correct
   - Check HMAC signature generation
   - Ensure nonce is current timestamp

3. **Database Issues**
   - Verify Supabase credentials and permissions
   - Check if tables and stored procedures exist
   - Monitor DuckDB file permissions

4. **Performance Issues**
   - Adjust BATCH_SIZE based on API limits
   - Increase POLL_INTERVAL if rate-limited
   - Monitor memory usage with large batches

### Debug Mode

```bash
# Run with debug logging
LOG_LEVEL=debug bun src/cdr_processor.js

# Enable all debugging
DEBUG=* LOG_LEVEL=debug bun src/cdr_processor.js
```

## Development

### Project Structure

```
phantom-billing-service/
├── src/
│   ├── lib/
│   │   ├── duckdb_client.js    # Local state management
│   │   ├── magnus_client.js    # MagnusBilling API client
│   │   ├── supabase_client.js  # Supabase database client
│   │   └── logger.js           # Logging and metrics
│   ├── cdr_processor.js        # Main service entry point
│   └── config.js               # Configuration management
├── build/                      # Build output (generated)
│   ├── cdr_processor.js        # Production bundle
│   └── cdr_processor.js.map    # Source map (dev builds)
├── logs/                       # PM2 log files (generated)
├── sql/
│   └── supabase_setup.sql      # Database schema and procedures
├── ecosystem.config.cjs        # PM2 configuration
├── deploy.sh                   # Production deployment script
├── dev.sh                      # Development setup script
├── .env.example                # Environment variables template
└── README.md                   # This file
```

### Adding Features

1. **Custom Rate Logic**: Modify `calculateCharge()` in `magnus_client.js`
2. **Additional Metrics**: Extend `Logger` class methods
3. **New API Endpoints**: Add methods to `MagnusBillingClient`
4. **Enhanced Validation**: Update `validateCDR()` function

## Security

- API credentials stored in environment variables
- HMAC-SHA512 authentication for MagnusBilling
- Service role key for Supabase (server-side only)
- No sensitive data in logs
- Secure file permissions for state database

## Performance

- **Batch Processing**: Configurable batch sizes for optimal throughput
- **Local State**: DuckDB in-process database for minimal latency
- **Sequential Processing**: Prevents race conditions and ensures consistency
- **Connection Pooling**: Efficient database connection management
- **Memory Management**: Streaming processing for large datasets

## License

Private - Phantom Billing Service
