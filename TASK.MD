### **Technical Prompt: Implement `cold_start.txt` Feature for Fast Startup and State Recovery**

#### **1. Objective**

Enhance the service's startup and data retention logic to handle "cold starts" (when the `phantom_billing.duckdb` database is missing) efficiently. The goal is to prevent the slow reprocessing of the entire data history by using a state recovery file, `cold_start.txt`.

#### **2. Problem Context**

Currently, if `phantom_billing.duckdb` is deleted, the service starts fetching data from the very beginning (timestamp 0). This leads to a massive performance bottleneck, as it tries to insert millions of historical records that already exist in Supabase. This triggers a slow, one-by-one fallback mechanism which is highly inefficient. The new feature will bypass this by using a recovery file.

#### **3. Proposed Solution: `cold_start.txt`**

A simple text file named `cold_start.txt` will be used to store the last known safe timestamp. This file will be automatically created and updated during the data cleanup process.

#### **4. Implementation Details**

The implementation consists of two parts: modifying the startup logic to read the file, and modifying the cleanup logic to write the file.

**Part A: Modify Startup Logic to Read `cold_start.txt`**

*   **File to Modify**: `src/cdr_processor.js`
*   **Function to Modify**: The `start()` method.

**Logic:**

1.  In the `start()` method, before the main processing loop begins but after `config` is loaded, you need to check if the DuckDB file exists (`fs.existsSync(config.database.statePath)`).
2.  **If `phantom_billing.duckdb` exists**: The logic remains unchanged. The service will initialize `StateClient` which will read the `last_fetch_time` from the existing database.
3.  **If `phantom_billing.duckdb` does NOT exist**:
    *   Check for the existence of `cold_start.txt` in the same directory (`path.dirname(config.database.statePath)`).
    *   If `cold_start.txt` exists, read the Unix timestamp from it.
    *   When initializing `StateClient`, you will need to pass this timestamp to it. This may require adding a new method to `StateClient`, such as `initializeWithTimestamp(ts)`, which will set the initial `last_fetch_time` to this value in the newly created DuckDB.
    *   If `cold_start.txt` also does not exist, the service should start as it does now (from timestamp 0).

**Relevant Code Snippet from `src/cdr_processor.js`:**

```javascript
// src/cdr_processor.js

class CDRProcessor {
    // ... constructor ...

    // ... other methods ...

    async start() {
        try {
            this.logger.logStartup(config);
            logConfig();

            // <<< NEW LOGIC GOES HERE >>>
            // 1. Check if duckdb file exists.
            // 2. If not, check for cold_start.txt.
            // 3. Read timestamp from cold_start.txt if it exists.
            // 4. Pass this timestamp to the stateClient initialization.

            // Initialize state management
            this.stateClient.initialize(); // This might need to be changed to e.g., this.stateClient.initialize(coldStartTimestamp);
            
            // ... rest of the function
        } catch (error) {
            this.logger.logError('Service startup', error);
            process.exit(1);
        }
    }
}
```

---

**Part B: Automating `cold_start.txt` Creation During Cleanup**

*   **File to Modify**: `src/lib/duckdb_client.js`
*   **Function to Modify**: `cleanupOldCDRs()`

**Logic:**

1.  The `cleanupOldCDRs()` function is called periodically to delete old, synced records from DuckDB.
2.  After the `DELETE` query successfully executes, add new logic to automatically create/update `cold_start.txt`.
3.  Inside the function, get the *current* `last_fetch_time` by calling `this.getStateValue('last_fetch_time')`.
4.  Construct the path for `cold_start.txt` (it should be in the same directory as `this.dbPath`).
5.  Use the Node.js `fs` module (e.g., `fs.writeFileSync`) to write the retrieved `last_fetch_time` value into `cold_start.txt`, overwriting the file if it exists.

**Relevant Code Snippet from `src/lib/duckdb_client.js`:**

```javascript
// src/lib/duckdb_client.js
const fs = require('fs'); // Make sure fs is required
const path = require('path'); // Make sure path is required

class StateClient {
    // ... other methods ...

    cleanupOldCDRs(daysToKeep = 90) {
        try {
            const cutoffDate = new Date();
            cutoffDate.setDate(cutoffDate.getDate() - daysToKeep);
            const cutoffTimestamp = cutoffDate.getTime();

            const result = this.executeQuery(`
                DELETE FROM cdr_processing
                WHERE processed_at < ${cutoffTimestamp} AND synced_to_supabase = 1
            `);

            console.log(`Cleaned up old CDRs older than ${daysToKeep} days`);

            // <<< NEW LOGIC GOES HERE >>>
            // 1. Get the current last_fetch_time using this.getStateValue('last_fetch_time').
            // 2. Define the path for cold_start.txt next to this.dbPath.
            // 3. Write the timestamp to the file using fs.writeFileSync.
            // 4. Add a log message indicating the file has been updated.

            return { deletedCount: result?.changes || 0 };
        } catch (error) {
            console.warn('Warning: Error cleaning up old CDRs:', error.message);
            return { deletedCount: 0 };
        }
    }

    // ... other methods ...
}
```

#### **5. Acceptance Criteria**

1.  When the service starts and `phantom_billing.duckdb` is missing, but `cold_start.txt` exists, the service must use the timestamp from the file as its starting point.
2.  The `cold_start.txt` file must be automatically created or updated with the current `last_fetch_time` every time the `cleanupOldCDRs` function is successfully executed.
3.  The service must function correctly both when the DuckDB exists and when it doesn't.
4.  The implementation should be robust and handle potential file system errors gracefully.
