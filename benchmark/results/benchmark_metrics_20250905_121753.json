{"summary": {"testConfiguration": {"totalRecords": 100000, "strategies": ["batch-1000", "batch-10000", "transaction-10000"], "dbPath": "phantom_billing_benchmark.duckdb", "timestamp": "2025-09-05T18:17:38.703Z", "environment": {"platform": "darwin", "arch": "arm64", "nodeVersion": "v24.3.0", "memoryLimit": 3400704}}, "totalTestDuration": "14612ms", "strategiesTesteed": 3, "totalRecordsProcessed": 100000, "performanceComparison": {"fastest": {"strategy": "<PERSON><PERSON> (1000)", "recordsPerSecond": 27801, "duration": 3597}, "slowest": {"strategy": "Transaction Insert (10000)", "recordsPerSecond": 0, "duration": 4}, "speedupRatio": 27801, "comparison": [{"strategy": "<PERSON><PERSON> (1000)", "recordsPerSecond": 27801, "duration": 3597, "errorRate": 0, "memoryPeak": -1416688}, {"strategy": "<PERSON><PERSON> (10000)", "recordsPerSecond": 22538, "duration": 4437, "errorRate": 0, "memoryPeak": 17507433}, {"strategy": "Transaction Insert (10000)", "recordsPerSecond": 0, "duration": 4, "errorRate": 0.6, "memoryPeak": 0}]}, "recommendations": [{"type": "performance", "message": "Best throughput: <PERSON><PERSON> (1000) at 27,801 records/second"}, {"type": "optimization", "message": "Batch Insert (1000) is 27801.0x faster than Transaction Insert (10000)"}, {"type": "memory", "message": "Most memory efficient: Batch Insert (1000) (-1.35 MB peak)"}, {"type": "reliability", "message": "Most reliable strategies: <PERSON><PERSON> Insert (1000), <PERSON><PERSON> Insert (10000) (< 1% error rate)"}]}, "detailedMetrics": [{"strategy": "<PERSON><PERSON> (1000)", "batchSize": 1000, "totalRecords": 100000, "totalBatches": 100, "successCount": 100000, "errorCount": 0, "duration": 3597, "recordsPerSecond": 27801, "memoryUsage": {"before": {"rss": 263602176, "heapTotal": 131542016, "heapUsed": 111284064, "external": 15500881, "arrayBuffers": 113}, "after": {"rss": 518520832, "heapTotal": 141422592, "heapUsed": 109867376, "external": 20878012, "arrayBuffers": 8701}, "peak": -1416688}, "errors": [], "timestamp": 1757096262820, "databaseSize": 65286144, "efficiency": {"throughput": 27801, "errorRate": 0, "memoryEfficiency": -1416688, "timePerRecord": 0.03597}}, {"strategy": "<PERSON><PERSON> (10000)", "batchSize": 10000, "totalRecords": 100000, "totalBatches": 10, "successCount": 100000, "errorCount": 0, "duration": 4437, "recordsPerSecond": 22538, "memoryUsage": {"before": {"rss": 624148480, "heapTotal": 101625856, "heapUsed": 87787883, "external": 23845292, "arrayBuffers": 113}, "after": {"rss": 846200832, "heapTotal": 103658496, "heapUsed": 105295316, "external": 40091775, "arrayBuffers": 1243}, "peak": 17507433}, "errors": [], "timestamp": 1757096270496, "databaseSize": 81276928, "efficiency": {"throughput": 22538, "errorRate": 0, "memoryEfficiency": 17507433, "timePerRecord": 0.04437}}, {"strategy": "Transaction Insert (10000)", "transactionSize": 10000, "totalRecords": 100000, "totalTransactions": 10, "successCount": 0, "errorCount": 60000, "duration": 4, "recordsPerSecond": 0, "memoryUsage": {"before": {"rss": 772898816, "heapTotal": 101492736, "heapUsed": 85882717, "external": 22448014, "arrayBuffers": 113}, "after": {"rss": 773111808, "heapTotal": 101525504, "heapUsed": 85882717, "external": 22448014, "arrayBuffers": 113}, "peak": 0}, "errors": [{"transaction": 0, "transactionSize": 10000, "error": "Invalid Error: Expected 8 parameters, but none were supplied"}, {"transaction": 1, "transactionSize": 10000, "error": "Invalid Error: Expected 8 parameters, but none were supplied"}, {"transaction": 2, "transactionSize": 10000, "error": "Invalid Error: Expected 8 parameters, but none were supplied"}, {"transaction": 3, "transactionSize": 10000, "error": "Invalid Error: Expected 8 parameters, but none were supplied"}, {"transaction": 4, "transactionSize": 10000, "error": "Invalid Error: Expected 8 parameters, but none were supplied"}, {"transaction": 5, "transactionSize": 10000, "error": "Invalid Error: Expected 8 parameters, but none were supplied"}], "timestamp": 1757096273314, "databaseSize": 81276928, "efficiency": {"throughput": 0, "errorRate": 0.6, "memoryEfficiency": 0, "timePerRecord": 4}}], "systemMetrics": [{"timestamp": 1757096258932, "memory": {"rss": 253657088, "heapTotal": 131442688, "heapUsed": 43272575, "external": 12631128, "arrayBuffers": 0}, "cpu": {"user": 424720, "system": 50309}}, {"timestamp": 1757096262821, "memory": {"rss": 560644096, "heapTotal": 141438976, "heapUsed": 109867376, "external": 20878690, "arrayBuffers": 9379}, "cpu": {"user": 4064458, "system": 236891}}, {"timestamp": 1757096264829, "memory": {"rss": 610959360, "heapTotal": 101542912, "heapUsed": 87787883, "external": 23845179, "arrayBuffers": 0}, "cpu": {"user": 4196467, "system": 299196}}, {"timestamp": 1757096270496, "memory": {"rss": 848297984, "heapTotal": 103664640, "heapUsed": 105295316, "external": 40092453, "arrayBuffers": 1921}, "cpu": {"user": 9614986, "system": 536173}}, {"timestamp": 1757096272501, "memory": {"rss": 770949120, "heapTotal": 101409792, "heapUsed": 85882717, "external": 22447901, "arrayBuffers": 0}, "cpu": {"user": 9672066, "system": 549892}}, {"timestamp": 1757096273314, "memory": {"rss": 772898816, "heapTotal": 101565440, "heapUsed": 85882717, "external": 22448692, "arrayBuffers": 791}, "cpu": {"user": 10536359, "system": 577268}}]}