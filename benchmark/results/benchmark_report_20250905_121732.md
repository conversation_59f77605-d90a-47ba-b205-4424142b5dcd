# DuckDB CDR Processing Performance Benchmark

**Generated**: September 5, 2025 at 12:17 PM

## Executive Summary

This benchmark tested DuckDB insertion performance using 10,000 CDR (Call Detail Records) across 1 different insertion strategies.

### Key Findings

- No comparative data available

### Test Configuration

- **Total Records**: 10,000
- **Test Duration**: 442ms
- **Platform**: darwin arm64
- **Node Version**: v24.3.0


## Detailed Results

| Strategy | Records/sec | Duration | Success Rate | Memory Peak | DB Size |
| --- | --- | --- | --- | --- | --- |
| Batch Insert (1000) | 29,851 | 335ms | 100.0% | 14.42 MB | 4.26 MB |

### Performance Metrics Explained

- **Records/sec**: Number of CDR records processed per second (higher is better)
- **Duration**: Total time taken to process all records
- **Success Rate**: Percentage of records successfully inserted
- **Memory Peak**: Peak memory usage during insertion
- **DB Size**: Final database file size after insertion


## Performance Comparison

```
Batch Insert (1000)  |██████████████████████████████████████████████████| 29,851 rec/sec
```


## Memory Usage Analysis

| Strategy | Memory Peak | Efficiency (rec/MB) |
| --- | --- | --- |
| Batch Insert (1000) | 14.42 MB | 2070 |

**Memory Efficiency** is calculated as records processed per MB of peak memory usage. Higher values indicate better memory utilization.


## Recommendations

No specific recommendations available based on current test results.

## Technical Details

### Test Environment

- **Platform**: darwin
- **Architecture**: arm64
- **Node.js Version**: v24.3.0
- **Memory Limit**: 3.24 MB

### CDR Record Schema

Each CDR record contains the following fields:
- `cdr_id`: Unique identifier for the call
- `sip_user`: SIP user making the call
- `duration`: Call duration in seconds
- `charge`: Calculated charge for the call
- `cost`: Provider cost for the call
- `start_time`: Call start timestamp
- `raw_data`: Complete MagnusBilling API response

### Database Schema

```sql
CREATE TABLE cdr_processing (
    cdr_id VARCHAR PRIMARY KEY,
    sip_user VARCHAR NOT NULL,
    duration DOUBLE NOT NULL,
    charge DOUBLE NOT NULL,
    cost DOUBLE DEFAULT 0,
    start_time TIMESTAMP NOT NULL,
    processed_at TIMESTAMP,
    synced_to_supabase BOOLEAN DEFAULT FALSE,
    raw_data VARCHAR
);

-- Indexes for performance
CREATE INDEX idx_cdr_id ON cdr_processing(cdr_id);
CREATE INDEX idx_cdr_synced ON cdr_processing(synced_to_supabase);
CREATE INDEX idx_cdr_processed_at ON cdr_processing(processed_at);
```

### Insertion Strategies

1. **Single Insert**: Individual INSERT statements for each record
2. **Batch Insert**: Multi-value INSERT statements with configurable batch sizes
3. **Transaction Insert**: Batched INSERTs wrapped in transactions for ACID compliance


---

## Appendix

### Raw Test Data

For detailed analysis, see the accompanying JSON and CSV files:
- `benchmark_metrics_*.json` - Complete test metrics and system data
- `benchmark_results_*.csv` - Tabular data suitable for spreadsheet analysis

### Reproducing Results

To reproduce these results, run:

```bash
# Quick test (10k records)
bun benchmark --quick

# Full test (100k records) 
bun benchmark --full

# Custom configuration
bun benchmark --records 50000 --strategies batch-1000 batch-5000 transaction-10000
```

### Notes

- Results may vary based on system specifications and current load
- DuckDB performance is highly dependent on available memory and storage type
- For production use, test with realistic data volumes and patterns
- Consider implementing connection pooling for high-concurrency scenarios

*Benchmark generated by Phantom Billing Service Performance Suite*
