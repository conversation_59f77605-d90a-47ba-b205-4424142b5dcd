# DuckDB CDR Processing Performance Benchmark

**Generated**: September 5, 2025 at 12:17 PM

## Executive Summary

This benchmark tested DuckDB insertion performance using 100,000 CDR (Call Detail Records) across 3 different insertion strategies.

### Key Findings


- **Best Performing Strategy**: Batch Insert (1000) achieved 27,801 records/second
- **Performance Range**: 27801.0x difference between fastest and slowest strategies
- **Recommended Approach**: Batch Insert (1000) for optimal throughput


### Test Configuration

- **Total Records**: 100,000
- **Test Duration**: 14612ms
- **Platform**: darwin arm64
- **Node Version**: v24.3.0


## Detailed Results

| Strategy | Records/sec | Duration | Success Rate | Memory Peak | DB Size |
| --- | --- | --- | --- | --- | --- |
| Batch Insert (1000) | 27,801 | 3.60s | 100.0% | -1.35 MB | 62.26 MB |
| Batch Insert (10000) | 22,538 | 4.44s | 100.0% | 16.7 MB | 77.51 MB |
| Transaction Insert (10000) | 0 | 4ms | 0.0% | 0 B | 77.51 MB |

### Performance Metrics Explained

- **Records/sec**: Number of CDR records processed per second (higher is better)
- **Duration**: Total time taken to process all records
- **Success Rate**: Percentage of records successfully inserted
- **Memory Peak**: Peak memory usage during insertion
- **DB Size**: Final database file size after insertion


## Performance Comparison

```
Batch Insert (1000)  |██████████████████████████████████████████████████| 27,801 rec/sec
Batch Insert (10000) |█████████████████████████████████████████░░░░░░░░░| 22,538 rec/sec
Transaction Insert (10000) |░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░| 0 rec/sec
```


## Memory Usage Analysis

| Strategy | Memory Peak | Efficiency (rec/MB) |
| --- | --- | --- |
| Batch Insert (1000) | -1.35 MB | -20577 |
| Batch Insert (10000) | 16.7 MB | 1350 |
| Transaction Insert (10000) | 0 B | NaN |

**Memory Efficiency** is calculated as records processed per MB of peak memory usage. Higher values indicate better memory utilization.


## Recommendations

1. **Performance**: Best throughput: Batch Insert (1000) at 27,801 records/second
2. **Optimization**: Batch Insert (1000) is 27801.0x faster than Transaction Insert (10000)
3. **Memory**: Most memory efficient: Batch Insert (1000) (-1.35 MB peak)
4. **Reliability**: Most reliable strategies: Batch Insert (1000), Batch Insert (10000) (< 1% error rate)

### General Guidelines

- For **high-throughput scenarios**: Use the fastest strategy identified above
- For **memory-constrained environments**: Consider the most memory-efficient option
- For **reliability-critical applications**: Prefer strategies with low error rates
- **Batch sizes**: Larger batches generally perform better but consume more memory
- **Transactions**: Provide ACID guarantees but may reduce throughput


## Technical Details

### Test Environment

- **Platform**: darwin
- **Architecture**: arm64
- **Node.js Version**: v24.3.0
- **Memory Limit**: 3.24 MB

### CDR Record Schema

Each CDR record contains the following fields:
- `cdr_id`: Unique identifier for the call
- `sip_user`: SIP user making the call
- `duration`: Call duration in seconds
- `charge`: Calculated charge for the call
- `cost`: Provider cost for the call
- `start_time`: Call start timestamp
- `raw_data`: Complete MagnusBilling API response

### Database Schema

```sql
CREATE TABLE cdr_processing (
    cdr_id VARCHAR PRIMARY KEY,
    sip_user VARCHAR NOT NULL,
    duration DOUBLE NOT NULL,
    charge DOUBLE NOT NULL,
    cost DOUBLE DEFAULT 0,
    start_time TIMESTAMP NOT NULL,
    processed_at TIMESTAMP,
    synced_to_supabase BOOLEAN DEFAULT FALSE,
    raw_data VARCHAR
);

-- Indexes for performance
CREATE INDEX idx_cdr_id ON cdr_processing(cdr_id);
CREATE INDEX idx_cdr_synced ON cdr_processing(synced_to_supabase);
CREATE INDEX idx_cdr_processed_at ON cdr_processing(processed_at);
```

### Insertion Strategies

1. **Single Insert**: Individual INSERT statements for each record
2. **Batch Insert**: Multi-value INSERT statements with configurable batch sizes
3. **Transaction Insert**: Batched INSERTs wrapped in transactions for ACID compliance


---

## Appendix

### Raw Test Data

For detailed analysis, see the accompanying JSON and CSV files:
- `benchmark_metrics_*.json` - Complete test metrics and system data
- `benchmark_results_*.csv` - Tabular data suitable for spreadsheet analysis

### Reproducing Results

To reproduce these results, run:

```bash
# Quick test (10k records)
bun benchmark --quick

# Full test (100k records) 
bun benchmark --full

# Custom configuration
bun benchmark --records 50000 --strategies batch-1000 batch-5000 transaction-10000
```

### Notes

- Results may vary based on system specifications and current load
- DuckDB performance is highly dependent on available memory and storage type
- For production use, test with realistic data volumes and patterns
- Consider implementing connection pooling for high-concurrency scenarios

*Benchmark generated by Phantom Billing Service Performance Suite*
