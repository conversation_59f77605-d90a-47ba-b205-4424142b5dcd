{"summary": {"testConfiguration": {"totalRecords": 10000, "strategies": ["batch-1000"], "dbPath": "phantom_billing_benchmark.duckdb", "timestamp": "2025-09-05T18:17:32.333Z", "environment": {"platform": "darwin", "arch": "arm64", "nodeVersion": "v24.3.0", "memoryLimit": 3400704}}, "totalTestDuration": "442ms", "strategiesTesteed": 1, "totalRecordsProcessed": 10000, "performanceComparison": null, "recommendations": []}, "detailedMetrics": [{"strategy": "<PERSON><PERSON> (1000)", "batchSize": 1000, "totalRecords": 10000, "totalBatches": 10, "successCount": 10000, "errorCount": 0, "duration": 335, "recordsPerSecond": 29851, "memoryUsage": {"before": {"rss": 101433344, "heapTotal": 23900160, "heapUsed": 230249, "external": 39402, "arrayBuffers": 113}, "after": {"rss": 141000704, "heapTotal": 25675776, "heapUsed": 15352664, "external": 4966767, "arrayBuffers": 1243}, "peak": 15122415}, "errors": [], "timestamp": 1757096252775, "databaseSize": 4468736, "efficiency": {"throughput": 29851, "errorRate": 0, "memoryEfficiency": 15122415, "timePerRecord": 0.0335}}], "systemMetrics": [{"timestamp": 1757096252362, "memory": {"rss": 92962816, "heapTotal": 23768064, "heapUsed": 230249, "external": 39289, "arrayBuffers": 0}, "cpu": {"user": 129554, "system": 63629}}, {"timestamp": 1757096252775, "memory": {"rss": 145653760, "heapTotal": 25726976, "heapUsed": 15352664, "external": 4967445, "arrayBuffers": 1921}, "cpu": {"user": 467229, "system": 97423}}]}