const fs = require('fs');
const path = require('path');
const { format } = require('date-fns');

class MetricsCollector {
    constructor() {
        this.metrics = [];
        this.systemMetrics = [];
        this.testStartTime = null;
        this.testEndTime = null;
        this.testConfig = null;
    }

    // Set test configuration
    setConfig(config) {
        this.testConfig = {
            totalRecords: config.records || 0,
            strategies: config.strategies || [],
            dbPath: config.dbPath || 'unknown',
            timestamp: new Date().toISOString(),
            environment: {
                platform: process.platform,
                arch: process.arch,
                nodeVersion: process.version,
                memoryLimit: process.memoryUsage().heapTotal
            }
        };
    }

    // Start test timing
    startTest() {
        this.testStartTime = Date.now();
        console.log('[Metrics] Test started at:', new Date(this.testStartTime).toISOString());
    }

    // End test timing
    endTest() {
        this.testEndTime = Date.now();
        console.log('[Metrics] Test completed at:', new Date(this.testEndTime).toISOString());
        console.log('[Metrics] Total test duration:', this.getTotalDuration());
    }

    // Get total test duration
    getTotalDuration() {
        if (!this.testStartTime || !this.testEndTime) {
            return 'N/A';
        }
        return `${this.testEndTime - this.testStartTime}ms`;
    }

    // Collect system metrics
    collectSystemMetrics() {
        const memoryUsage = process.memoryUsage();
        const cpuUsage = process.cpuUsage();
        
        const metrics = {
            timestamp: Date.now(),
            memory: {
                rss: memoryUsage.rss,
                heapTotal: memoryUsage.heapTotal,
                heapUsed: memoryUsage.heapUsed,
                external: memoryUsage.external,
                arrayBuffers: memoryUsage.arrayBuffers
            },
            cpu: {
                user: cpuUsage.user,
                system: cpuUsage.system
            }
        };

        this.systemMetrics.push(metrics);
        return metrics;
    }

    // Get database file size
    getDbFileSize(dbPath) {
        try {
            if (fs.existsSync(dbPath)) {
                const stats = fs.statSync(dbPath);
                return stats.size;
            }
        } catch (error) {
            console.warn('[Metrics] Failed to get database file size:', error.message);
        }
        return 0;
    }

    // Add strategy metrics
    addStrategyMetrics(strategyMetrics) {
        const dbSize = this.getDbFileSize(this.testConfig?.dbPath || 'phantom_billing_benchmark.duckdb');
        
        const enhancedMetrics = {
            ...strategyMetrics,
            timestamp: Date.now(),
            databaseSize: dbSize,
            efficiency: {
                throughput: strategyMetrics.recordsPerSecond || 0,
                errorRate: strategyMetrics.errorCount / (strategyMetrics.totalRecords || 1),
                memoryEfficiency: this.calculateMemoryEfficiency(strategyMetrics.memoryUsage),
                timePerRecord: strategyMetrics.duration / (strategyMetrics.successCount || 1)
            }
        };

        this.metrics.push(enhancedMetrics);
        
        console.log(`[Metrics] Collected metrics for ${strategyMetrics.strategy}:`);
        console.log(`  Records/sec: ${strategyMetrics.recordsPerSecond?.toLocaleString() || 'N/A'}`);
        console.log(`  Success rate: ${((strategyMetrics.successCount / (strategyMetrics.totalRecords || 1)) * 100).toFixed(1)}%`);
        console.log(`  Memory peak: ${this.formatBytes(enhancedMetrics.efficiency.memoryEfficiency)}`);
    }

    // Calculate memory efficiency
    calculateMemoryEfficiency(memoryUsage) {
        if (!memoryUsage || !memoryUsage.before || !memoryUsage.after) {
            return 0;
        }
        return memoryUsage.after.heapUsed - memoryUsage.before.heapUsed;
    }

    // Format bytes to human readable
    formatBytes(bytes) {
        if (bytes === 0) return '0 B';
        
        const k = 1024;
        const sizes = ['B', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(Math.abs(bytes)) / Math.log(k));
        
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }

    // Format duration to human readable
    formatDuration(ms) {
        if (ms < 1000) return `${ms}ms`;
        if (ms < 60000) return `${(ms / 1000).toFixed(2)}s`;
        return `${(ms / 60000).toFixed(2)}m`;
    }

    // Get performance comparison
    getComparison() {
        if (this.metrics.length < 2) return null;

        const sortedByThroughput = [...this.metrics].sort((a, b) => 
            (b.recordsPerSecond || 0) - (a.recordsPerSecond || 0)
        );

        const fastest = sortedByThroughput[0];
        const slowest = sortedByThroughput[sortedByThroughput.length - 1];

        return {
            fastest: {
                strategy: fastest.strategy,
                recordsPerSecond: fastest.recordsPerSecond || 0,
                duration: fastest.duration
            },
            slowest: {
                strategy: slowest.strategy,
                recordsPerSecond: slowest.recordsPerSecond || 0,
                duration: slowest.duration
            },
            speedupRatio: (fastest.recordsPerSecond || 0) / (slowest.recordsPerSecond || 1),
            comparison: this.metrics.map(m => ({
                strategy: m.strategy,
                recordsPerSecond: m.recordsPerSecond || 0,
                duration: m.duration,
                errorRate: m.efficiency.errorRate,
                memoryPeak: m.efficiency.memoryEfficiency
            }))
        };
    }

    // Generate summary statistics
    getSummary() {
        if (this.metrics.length === 0) {
            return { error: 'No metrics collected' };
        }

        const totalRecords = this.metrics[0]?.totalRecords || 0;
        const comparison = this.getComparison();

        return {
            testConfiguration: this.testConfig,
            totalTestDuration: this.getTotalDuration(),
            strategiesTesteed: this.metrics.length,
            totalRecordsProcessed: totalRecords,
            performanceComparison: comparison,
            recommendations: this.generateRecommendations(comparison)
        };
    }

    // Generate performance recommendations
    generateRecommendations(comparison) {
        if (!comparison) return [];

        const recommendations = [];

        // Throughput recommendation
        if (comparison.fastest) {
            recommendations.push({
                type: 'performance',
                message: `Best throughput: ${comparison.fastest.strategy} at ${comparison.fastest.recordsPerSecond?.toLocaleString()} records/second`
            });
        }

        // Speed improvement
        if (comparison.speedupRatio > 2) {
            recommendations.push({
                type: 'optimization',
                message: `${comparison.fastest.strategy} is ${comparison.speedupRatio.toFixed(1)}x faster than ${comparison.slowest.strategy}`
            });
        }

        // Memory efficiency
        const memoryEfficient = [...this.metrics].sort((a, b) => 
            a.efficiency.memoryEfficiency - b.efficiency.memoryEfficiency
        )[0];

        if (memoryEfficient) {
            recommendations.push({
                type: 'memory',
                message: `Most memory efficient: ${memoryEfficient.strategy} (${this.formatBytes(memoryEfficient.efficiency.memoryEfficiency)} peak)`
            });
        }

        // Error rate
        const lowErrorRate = this.metrics.filter(m => m.efficiency.errorRate < 0.01);
        if (lowErrorRate.length > 0) {
            recommendations.push({
                type: 'reliability',
                message: `Most reliable strategies: ${lowErrorRate.map(m => m.strategy).join(', ')} (< 1% error rate)`
            });
        }

        return recommendations;
    }

    // Export metrics to CSV
    async exportToCSV(filename = null) {
        if (!filename) {
            filename = `benchmark_results_${format(new Date(), 'yyyyMMdd_HHmmss')}.csv`;
        }

        const csvPath = path.join('results', filename);
        
        // Ensure results directory exists
        const resultsDir = path.dirname(csvPath);
        if (!fs.existsSync(resultsDir)) {
            fs.mkdirSync(resultsDir, { recursive: true });
        }

        // Prepare CSV data
        const csvHeader = [
            'Strategy',
            'Total Records',
            'Success Count',
            'Error Count',
            'Duration (ms)',
            'Records/Second',
            'Error Rate (%)',
            'Memory Peak (MB)',
            'Database Size (MB)',
            'Time per Record (ms)'
        ].join(',');

        const csvRows = this.metrics.map(m => [
            `"${m.strategy}"`,
            m.totalRecords || 0,
            m.successCount || 0,
            m.errorCount || 0,
            m.duration || 0,
            m.recordsPerSecond || 0,
            (m.efficiency.errorRate * 100).toFixed(2),
            (m.efficiency.memoryEfficiency / 1024 / 1024).toFixed(2),
            (m.databaseSize / 1024 / 1024).toFixed(2),
            m.efficiency.timePerRecord?.toFixed(4) || 0
        ].join(','));

        const csvContent = [csvHeader, ...csvRows].join('\n');

        fs.writeFileSync(csvPath, csvContent);
        console.log(`[Metrics] CSV exported to: ${csvPath}`);
        
        return csvPath;
    }

    // Export full metrics to JSON
    async exportToJSON(filename = null) {
        if (!filename) {
            filename = `benchmark_metrics_${format(new Date(), 'yyyyMMdd_HHmmss')}.json`;
        }

        const jsonPath = path.join('results', filename);
        
        // Ensure results directory exists
        const resultsDir = path.dirname(jsonPath);
        if (!fs.existsSync(resultsDir)) {
            fs.mkdirSync(resultsDir, { recursive: true });
        }

        const fullMetrics = {
            summary: this.getSummary(),
            detailedMetrics: this.metrics,
            systemMetrics: this.systemMetrics
        };

        fs.writeFileSync(jsonPath, JSON.stringify(fullMetrics, null, 2));
        console.log(`[Metrics] JSON exported to: ${jsonPath}`);
        
        return jsonPath;
    }

    // Print summary to console
    printSummary() {
        console.log('\n' + '='.repeat(80));
        console.log('BENCHMARK SUMMARY');
        console.log('='.repeat(80));

        const summary = this.getSummary();
        
        if (summary.error) {
            console.log('Error:', summary.error);
            return;
        }

        console.log(`Total Records Tested: ${summary.totalRecordsProcessed?.toLocaleString()}`);
        console.log(`Strategies Tested: ${summary.strategiesTesteed}`);
        console.log(`Total Test Duration: ${summary.totalTestDuration}`);

        if (summary.performanceComparison) {
            const comp = summary.performanceComparison;
            console.log(`\nFastest: ${comp.fastest.strategy} (${comp.fastest.recordsPerSecond?.toLocaleString()} rec/sec)`);
            console.log(`Slowest: ${comp.slowest.strategy} (${comp.slowest.recordsPerSecond?.toLocaleString()} rec/sec)`);
            console.log(`Speed Difference: ${comp.speedupRatio?.toFixed(1)}x faster`);
        }

        console.log('\nDetailed Results:');
        this.metrics.forEach((m, index) => {
            console.log(`${index + 1}. ${m.strategy}:`);
            console.log(`   Throughput: ${m.recordsPerSecond?.toLocaleString() || 'N/A'} records/second`);
            console.log(`   Duration: ${this.formatDuration(m.duration)}`);
            console.log(`   Success Rate: ${((m.successCount / (m.totalRecords || 1)) * 100).toFixed(1)}%`);
            console.log(`   Memory Peak: ${this.formatBytes(m.efficiency.memoryEfficiency)}`);
        });

        if (summary.recommendations && summary.recommendations.length > 0) {
            console.log('\nRecommendations:');
            summary.recommendations.forEach((rec, index) => {
                console.log(`${index + 1}. [${rec.type.toUpperCase()}] ${rec.message}`);
            });
        }

        console.log('='.repeat(80));
    }
}

module.exports = MetricsCollector;
