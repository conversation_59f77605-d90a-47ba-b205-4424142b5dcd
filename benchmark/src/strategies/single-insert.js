const { open } = require('@evan/duckdb');

class SingleInsertStrategy {
    constructor(dbPath = 'phantom_billing_benchmark.duckdb') {
        this.dbPath = dbPath;
        this.db = null;
        this.connection = null;
        this.name = 'Single Insert';
        this.description = 'Insert records one by one with individual queries';
    }

    // Initialize database connection and create table
    async initialize() {
        try {
            console.log(`[${this.name}] Initializing database: ${this.dbPath}`);
            
            this.db = open(this.dbPath);
            this.connection = this.db.connect();

            // Create CDR processing table
            this.connection.query(`
                CREATE TABLE IF NOT EXISTS cdr_processing (
                    cdr_id VARCHAR PRIMARY KEY,
                    sip_user VARCHAR NOT NULL,
                    duration DOUBLE NOT NULL,
                    charge DOUBLE NOT NULL,
                    cost DOUBLE DEFAULT 0,
                    start_time TIMESTAMP NOT NULL,
                    processed_at TIMESTAMP,
                    synced_to_supabase BOOLEAN DEFAULT FALSE,
                    raw_data VARCHAR
                )
            `);

            // Create indexes for better performance
            this.connection.query(`CREATE INDEX IF NOT EXISTS idx_cdr_id ON cdr_processing(cdr_id)`);
            this.connection.query(`CREATE INDEX IF NOT EXISTS idx_cdr_synced ON cdr_processing(synced_to_supabase)`);
            this.connection.query(`CREATE INDEX IF NOT EXISTS idx_cdr_processed_at ON cdr_processing(processed_at)`);

            console.log(`[${this.name}] Database initialized successfully`);
        } catch (error) {
            console.error(`[${this.name}] Failed to initialize database:`, error);
            throw error;
        }
    }

    // Clean the database for fresh testing
    async cleanup() {
        try {
            console.log(`[${this.name}] Cleaning database...`);
            this.connection.query('DELETE FROM cdr_processing');
            console.log(`[${this.name}] Database cleaned successfully`);
        } catch (error) {
            console.warn(`[${this.name}] Warning: Failed to clean database:`, error.message);
        }
    }

    // Insert records one by one
    async insertRecords(records) {
        console.log(`[${this.name}] Starting insertion of ${records.length.toLocaleString()} records...`);
        
        const startTime = Date.now();
        const startMemory = process.memoryUsage();
        
        let successCount = 0;
        let errorCount = 0;
        const errors = [];
        
        // Progress tracking
        const totalRecords = records.length;
        const progressInterval = Math.max(1, Math.floor(totalRecords / 20)); // Report every 5%

        try {
            for (let i = 0; i < records.length; i++) {
                const record = records[i];
                
                try {
                    const now = new Date().toISOString();
                    
                    // Individual INSERT statement
                    this.connection.query(`
                        INSERT INTO cdr_processing (
                            cdr_id, sip_user, duration, charge, cost, start_time, processed_at, raw_data
                        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?)
                        ON CONFLICT (cdr_id) DO NOTHING
                    `, [
                        record.cdr_id,
                        record.sip_user,
                        record.duration,
                        record.charge,
                        record.cost,
                        record.start_time,
                        now,
                        JSON.stringify(record.raw_data || {})
                    ]);

                    successCount++;
                } catch (error) {
                    errorCount++;
                    errors.push({
                        cdr_id: record.cdr_id,
                        error: error.message
                    });
                    
                    // Stop if too many errors
                    if (errorCount > 100) {
                        console.error(`[${this.name}] Too many errors, stopping insertion`);
                        break;
                    }
                }

                // Progress reporting
                if ((i + 1) % progressInterval === 0 || i === records.length - 1) {
                    const progress = ((i + 1) / totalRecords * 100).toFixed(1);
                    const currentTime = Date.now();
                    const elapsedSeconds = (currentTime - startTime) / 1000;
                    const recordsPerSecond = Math.round((i + 1) / elapsedSeconds);
                    
                    console.log(`[${this.name}] Progress: ${progress}% (${(i + 1).toLocaleString()}/${totalRecords.toLocaleString()}) - ${recordsPerSecond.toLocaleString()} rec/sec`);
                }
            }
        } catch (error) {
            console.error(`[${this.name}] Fatal error during insertion:`, error);
            throw error;
        }

        const endTime = Date.now();
        const endMemory = process.memoryUsage();
        
        const metrics = {
            strategy: this.name,
            totalRecords: records.length,
            successCount,
            errorCount,
            duration: endTime - startTime,
            recordsPerSecond: Math.round(successCount / ((endTime - startTime) / 1000)),
            memoryUsage: {
                before: startMemory,
                after: endMemory,
                peak: endMemory.heapUsed - startMemory.heapUsed
            },
            errors: errors.slice(0, 10) // Only keep first 10 errors for reporting
        };

        console.log(`[${this.name}] Completed: ${successCount.toLocaleString()} successful, ${errorCount} errors in ${metrics.duration}ms (${metrics.recordsPerSecond.toLocaleString()} rec/sec)`);
        
        return metrics;
    }

    // Get database statistics
    getStats() {
        try {
            const countResult = this.connection.query('SELECT COUNT(*) as count FROM cdr_processing');
            const sizeResult = this.connection.query(`
                SELECT 
                    COUNT(*) as total_records,
                    AVG(duration) as avg_duration,
                    AVG(charge) as avg_charge,
                    MIN(processed_at) as first_record,
                    MAX(processed_at) as last_record
                FROM cdr_processing
            `);

            return {
                totalRecords: countResult[0].count,
                ...sizeResult[0]
            };
        } catch (error) {
            console.warn(`[${this.name}] Warning: Failed to get stats:`, error.message);
            return { totalRecords: 0 };
        }
    }

    // Close database connection
    async close() {
        try {
            if (this.connection) {
                this.connection.close();
                this.connection = null;
            }
            if (this.db) {
                this.db.close();
                this.db = null;
            }
            console.log(`[${this.name}] Database connection closed`);
        } catch (error) {
            console.warn(`[${this.name}] Warning: Error closing database:`, error.message);
        }
    }
}

module.exports = SingleInsertStrategy;
