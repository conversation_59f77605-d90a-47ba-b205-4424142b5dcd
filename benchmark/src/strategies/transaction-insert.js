const { open } = require('@evan/duckdb');

class TransactionInsertStrategy {
    constructor(dbPath = 'phantom_billing_benchmark.duckdb', transactionSize = 10000) {
        this.dbPath = dbPath;
        this.db = null;
        this.connection = null;
        this.transactionSize = transactionSize;
        this.name = `Transaction Insert (${transactionSize})`;
        this.description = `Insert records within transactions of ${transactionSize} records each`;
    }

    // Initialize database connection and create table
    async initialize() {
        try {
            console.log(`[${this.name}] Initializing database: ${this.dbPath}`);
            
            this.db = open(this.dbPath);
            this.connection = this.db.connect();

            // Create CDR processing table
            this.connection.query(`
                CREATE TABLE IF NOT EXISTS cdr_processing (
                    cdr_id VARCHAR PRIMARY KEY,
                    sip_user VARCHAR NOT NULL,
                    duration DOUBLE NOT NULL,
                    charge DOUBLE NOT NULL,
                    cost DOUBLE DEFAULT 0,
                    start_time TIMESTAMP NOT NULL,
                    processed_at TIMESTAMP,
                    synced_to_supabase BOOLEAN DEFAULT FALSE,
                    raw_data VARCHAR
                )
            `);

            // Create indexes for better performance
            this.connection.query(`CREATE INDEX IF NOT EXISTS idx_cdr_id ON cdr_processing(cdr_id)`);
            this.connection.query(`CREATE INDEX IF NOT EXISTS idx_cdr_synced ON cdr_processing(synced_to_supabase)`);
            this.connection.query(`CREATE INDEX IF NOT EXISTS idx_cdr_processed_at ON cdr_processing(processed_at)`);

            console.log(`[${this.name}] Database initialized successfully`);
        } catch (error) {
            console.error(`[${this.name}] Failed to initialize database:`, error);
            throw error;
        }
    }

    // Clean the database for fresh testing
    async cleanup() {
        try {
            console.log(`[${this.name}] Cleaning database...`);
            this.connection.query('DELETE FROM cdr_processing');
            console.log(`[${this.name}] Database cleaned successfully`);
        } catch (error) {
            console.warn(`[${this.name}] Warning: Failed to clean database:`, error.message);
        }
    }

    // Insert records within transactions
    async insertRecords(records) {
        console.log(`[${this.name}] Starting insertion of ${records.length.toLocaleString()} records in transactions of ${this.transactionSize.toLocaleString()}...`);
        
        const startTime = Date.now();
        const startMemory = process.memoryUsage();
        
        let successCount = 0;
        let errorCount = 0;
        const errors = [];
        
        // Split records into transaction chunks
        const transactions = [];
        for (let i = 0; i < records.length; i += this.transactionSize) {
            transactions.push(records.slice(i, i + this.transactionSize));
        }
        
        console.log(`[${this.name}] Processing ${transactions.length} transactions...`);

        try {
            for (let txIndex = 0; txIndex < transactions.length; txIndex++) {
                const transactionRecords = transactions[txIndex];
                
                try {
                    // Begin transaction
                    this.connection.query('BEGIN TRANSACTION');
                    
                    const now = new Date().toISOString();
                    
                    // Insert all records in this transaction
                    for (const record of transactionRecords) {
                        this.connection.query(`
                            INSERT INTO cdr_processing (
                                cdr_id, sip_user, duration, charge, cost, start_time, processed_at, raw_data
                            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?)
                            ON CONFLICT (cdr_id) DO NOTHING
                        `, [
                            record.cdr_id,
                            record.sip_user,
                            record.duration,
                            record.charge,
                            record.cost,
                            record.start_time,
                            now,
                            JSON.stringify(record.raw_data || {})
                        ]);
                    }
                    
                    // Commit transaction
                    this.connection.query('COMMIT');
                    
                    successCount += transactionRecords.length;
                } catch (error) {
                    // Rollback on error
                    try {
                        this.connection.query('ROLLBACK');
                    } catch (rollbackError) {
                        console.warn(`[${this.name}] Rollback failed:`, rollbackError.message);
                    }
                    
                    errorCount += transactionRecords.length;
                    errors.push({
                        transaction: txIndex,
                        transactionSize: transactionRecords.length,
                        error: error.message
                    });
                    
                    console.warn(`[${this.name}] Transaction ${txIndex + 1} failed: ${error.message}`);
                    
                    // Stop if too many transaction errors
                    if (errors.length > 5) {
                        console.error(`[${this.name}] Too many transaction errors, stopping insertion`);
                        break;
                    }
                }

                // Progress reporting
                const progress = ((txIndex + 1) / transactions.length * 100).toFixed(1);
                const currentTime = Date.now();
                const elapsedSeconds = (currentTime - startTime) / 1000;
                const recordsProcessed = Math.min((txIndex + 1) * this.transactionSize, records.length);
                const recordsPerSecond = Math.round(recordsProcessed / elapsedSeconds);
                
                if ((txIndex + 1) % Math.max(1, Math.floor(transactions.length / 20)) === 0 || txIndex === transactions.length - 1) {
                    console.log(`[${this.name}] Progress: ${progress}% (${txIndex + 1}/${transactions.length} transactions) - ${recordsPerSecond.toLocaleString()} rec/sec`);
                }
            }
        } catch (error) {
            console.error(`[${this.name}] Fatal error during transaction insertion:`, error);
            throw error;
        }

        const endTime = Date.now();
        const endMemory = process.memoryUsage();
        
        const metrics = {
            strategy: this.name,
            transactionSize: this.transactionSize,
            totalRecords: records.length,
            totalTransactions: transactions.length,
            successCount,
            errorCount,
            duration: endTime - startTime,
            recordsPerSecond: Math.round(successCount / ((endTime - startTime) / 1000)),
            memoryUsage: {
                before: startMemory,
                after: endMemory,
                peak: endMemory.heapUsed - startMemory.heapUsed
            },
            errors: errors.slice(0, 10) // Only keep first 10 errors for reporting
        };

        console.log(`[${this.name}] Completed: ${successCount.toLocaleString()} successful, ${errorCount} errors in ${metrics.duration}ms (${metrics.recordsPerSecond.toLocaleString()} rec/sec)`);
        
        return metrics;
    }

    // Get database statistics
    getStats() {
        try {
            const countResult = this.connection.query('SELECT COUNT(*) as count FROM cdr_processing');
            const sizeResult = this.connection.query(`
                SELECT 
                    COUNT(*) as total_records,
                    AVG(duration) as avg_duration,
                    AVG(charge) as avg_charge,
                    MIN(processed_at) as first_record,
                    MAX(processed_at) as last_record
                FROM cdr_processing
            `);

            return {
                totalRecords: countResult[0].count,
                ...sizeResult[0]
            };
        } catch (error) {
            console.warn(`[${this.name}] Warning: Failed to get stats:`, error.message);
            return { totalRecords: 0 };
        }
    }

    // Close database connection
    async close() {
        try {
            if (this.connection) {
                this.connection.close();
                this.connection = null;
            }
            if (this.db) {
                this.db.close();
                this.db = null;
            }
            console.log(`[${this.name}] Database connection closed`);
        } catch (error) {
            console.warn(`[${this.name}] Warning: Error closing database:`, error.message);
        }
    }
}

module.exports = TransactionInsertStrategy;
