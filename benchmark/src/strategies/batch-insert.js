const { open } = require('@evan/duckdb');

class BatchInsertStrategy {
    constructor(dbPath = 'phantom_billing_benchmark.duckdb', batchSize = 1000) {
        this.dbPath = dbPath;
        this.db = null;
        this.connection = null;
        this.batchSize = batchSize;
        this.name = `Batch Insert (${batchSize})`;
        this.description = `Insert records in batches of ${batchSize} with multi-value INSERT statements`;
    }

    // Initialize database connection and create table
    async initialize() {
        try {
            console.log(`[${this.name}] Initializing database: ${this.dbPath}`);
            
            this.db = open(this.dbPath);
            this.connection = this.db.connect();

            // Create CDR processing table
            this.connection.query(`
                CREATE TABLE IF NOT EXISTS cdr_processing (
                    cdr_id VARCHAR PRIMARY KEY,
                    sip_user VARCHAR NOT NULL,
                    duration DOUBLE NOT NULL,
                    charge DOUBLE NOT NULL,
                    cost DOUBLE DEFAULT 0,
                    start_time TIMESTAMP NOT NULL,
                    processed_at TIMESTAMP,
                    synced_to_supabase BOOLEAN DEFAULT FALSE,
                    raw_data VARCHAR
                )
            `);

            // Create indexes for better performance
            this.connection.query(`CREATE INDEX IF NOT EXISTS idx_cdr_id ON cdr_processing(cdr_id)`);
            this.connection.query(`CREATE INDEX IF NOT EXISTS idx_cdr_synced ON cdr_processing(synced_to_supabase)`);
            this.connection.query(`CREATE INDEX IF NOT EXISTS idx_cdr_processed_at ON cdr_processing(processed_at)`);

            console.log(`[${this.name}] Database initialized successfully`);
        } catch (error) {
            console.error(`[${this.name}] Failed to initialize database:`, error);
            throw error;
        }
    }

    // Clean the database for fresh testing
    async cleanup() {
        try {
            console.log(`[${this.name}] Cleaning database...`);
            this.connection.query('DELETE FROM cdr_processing');
            console.log(`[${this.name}] Database cleaned successfully`);
        } catch (error) {
            console.warn(`[${this.name}] Warning: Failed to clean database:`, error.message);
        }
    }

    // Create multi-value INSERT statement for a batch
    createBatchInsertSQL(batch) {
        const now = new Date().toISOString();
        
        // Create VALUES clauses for each record
        const valuesClauses = batch.map(record => {
            const escapedRawData = JSON.stringify(record.raw_data || {}).replace(/'/g, "''");
            const escapedSipUser = record.sip_user.replace(/'/g, "''");
            
            return `('${record.cdr_id}', '${escapedSipUser}', ${record.duration}, ${record.charge}, ${record.cost}, '${record.start_time}', '${now}', '${escapedRawData}')`;
        });

        return `
            INSERT INTO cdr_processing (
                cdr_id, sip_user, duration, charge, cost, start_time, processed_at, raw_data
            ) VALUES ${valuesClauses.join(', ')}
            ON CONFLICT (cdr_id) DO NOTHING
        `;
    }

    // Insert records in batches
    async insertRecords(records) {
        console.log(`[${this.name}] Starting insertion of ${records.length.toLocaleString()} records in batches of ${this.batchSize.toLocaleString()}...`);
        
        const startTime = Date.now();
        const startMemory = process.memoryUsage();
        
        let successCount = 0;
        let errorCount = 0;
        const errors = [];
        
        // Split records into batches
        const batches = [];
        for (let i = 0; i < records.length; i += this.batchSize) {
            batches.push(records.slice(i, i + this.batchSize));
        }
        
        console.log(`[${this.name}] Processing ${batches.length} batches...`);

        try {
            for (let batchIndex = 0; batchIndex < batches.length; batchIndex++) {
                const batch = batches[batchIndex];
                
                try {
                    const batchSQL = this.createBatchInsertSQL(batch);
                    this.connection.query(batchSQL);
                    
                    successCount += batch.length;
                } catch (error) {
                    errorCount += batch.length;
                    errors.push({
                        batch: batchIndex,
                        batchSize: batch.length,
                        error: error.message
                    });
                    
                    console.warn(`[${this.name}] Batch ${batchIndex + 1} failed: ${error.message}`);
                    
                    // Stop if too many batch errors
                    if (errors.length > 10) {
                        console.error(`[${this.name}] Too many batch errors, stopping insertion`);
                        break;
                    }
                }

                // Progress reporting
                const progress = ((batchIndex + 1) / batches.length * 100).toFixed(1);
                const currentTime = Date.now();
                const elapsedSeconds = (currentTime - startTime) / 1000;
                const recordsProcessed = (batchIndex + 1) * this.batchSize;
                const recordsPerSecond = Math.round(recordsProcessed / elapsedSeconds);
                
                if ((batchIndex + 1) % Math.max(1, Math.floor(batches.length / 20)) === 0 || batchIndex === batches.length - 1) {
                    console.log(`[${this.name}] Progress: ${progress}% (${batchIndex + 1}/${batches.length} batches) - ${recordsPerSecond.toLocaleString()} rec/sec`);
                }
            }
        } catch (error) {
            console.error(`[${this.name}] Fatal error during batch insertion:`, error);
            throw error;
        }

        const endTime = Date.now();
        const endMemory = process.memoryUsage();
        
        const metrics = {
            strategy: this.name,
            batchSize: this.batchSize,
            totalRecords: records.length,
            totalBatches: batches.length,
            successCount,
            errorCount,
            duration: endTime - startTime,
            recordsPerSecond: Math.round(successCount / ((endTime - startTime) / 1000)),
            memoryUsage: {
                before: startMemory,
                after: endMemory,
                peak: endMemory.heapUsed - startMemory.heapUsed
            },
            errors: errors.slice(0, 10) // Only keep first 10 errors for reporting
        };

        console.log(`[${this.name}] Completed: ${successCount.toLocaleString()} successful, ${errorCount} errors in ${metrics.duration}ms (${metrics.recordsPerSecond.toLocaleString()} rec/sec)`);
        
        return metrics;
    }

    // Get database statistics
    getStats() {
        try {
            const countResult = this.connection.query('SELECT COUNT(*) as count FROM cdr_processing');
            const sizeResult = this.connection.query(`
                SELECT 
                    COUNT(*) as total_records,
                    AVG(duration) as avg_duration,
                    AVG(charge) as avg_charge,
                    MIN(processed_at) as first_record,
                    MAX(processed_at) as last_record
                FROM cdr_processing
            `);

            return {
                totalRecords: countResult[0].count,
                ...sizeResult[0]
            };
        } catch (error) {
            console.warn(`[${this.name}] Warning: Failed to get stats:`, error.message);
            return { totalRecords: 0 };
        }
    }

    // Close database connection
    async close() {
        try {
            if (this.connection) {
                this.connection.close();
                this.connection = null;
            }
            if (this.db) {
                this.db.close();
                this.db = null;
            }
            console.log(`[${this.name}] Database connection closed`);
        } catch (error) {
            console.warn(`[${this.name}] Warning: Error closing database:`, error.message);
        }
    }
}

module.exports = BatchInsertStrategy;
