const { format, subDays, addMinutes } = require('date-fns');

class CDRGenerator {
    constructor(options = {}) {
        this.options = {
            // SIP user patterns
            sipUserPrefixes: ['1001', '2001', '3001', '4001', '5001', '6001', '7001', '8001', '9001'],
            sipDomains: ['phantom.local', 'sip.phantom.com', 'pbx.local'],
            
            // Duration patterns (in seconds)
            minDuration: 1,
            maxDuration: 3600, // 1 hour
            avgDuration: 180, // 3 minutes
            
            // Rate configuration
            defaultRate: 0.01, // per second
            rateVariation: 0.005, // ±0.005 variation
            
            // Time patterns
            startTimeRange: 30, // last 30 days
            
            // Cost patterns
            costMultiplier: 0.8, // cost is 80% of charge
            
            ...options
        };
        
        this.recordCounter = 0;
    }

    // Generate a UUID-like CDR ID
    generateCdrId() {
        const timestamp = Date.now().toString(36);
        const random = Math.random().toString(36).substr(2, 9);
        return `cdr_${timestamp}_${random}_${++this.recordCounter}`;
    }

    // Generate realistic SIP user
    generateSipUser() {
        const prefix = this.options.sipUserPrefixes[
            Math.floor(Math.random() * this.options.sipUserPrefixes.length)
        ];
        const extension = Math.floor(Math.random() * 1000).toString().padStart(3, '0');
        const domain = this.options.sipDomains[
            Math.floor(Math.random() * this.options.sipDomains.length)
        ];
        return `${prefix}${extension}@${domain}`;
    }

    // Generate call duration with realistic distribution
    generateDuration() {
        // Use log-normal distribution for more realistic call durations
        const u1 = Math.random();
        const u2 = Math.random();
        
        // Box-Muller transformation for normal distribution
        const z0 = Math.sqrt(-2 * Math.log(u1)) * Math.cos(2 * Math.PI * u2);
        
        // Transform to log-normal
        const logDuration = Math.log(this.options.avgDuration) + 0.5 * z0;
        let duration = Math.exp(logDuration);
        
        // Clamp to reasonable bounds
        duration = Math.max(this.options.minDuration, duration);
        duration = Math.min(this.options.maxDuration, duration);
        
        return Math.round(duration);
    }

    // Generate realistic start time
    generateStartTime() {
        const daysAgo = Math.floor(Math.random() * this.options.startTimeRange);
        const baseDate = subDays(new Date(), daysAgo);
        
        // Weight towards business hours (9 AM to 6 PM)
        const hour = this.generateBusinessHour();
        const minute = Math.floor(Math.random() * 60);
        const second = Math.floor(Math.random() * 60);
        
        const startTime = new Date(baseDate);
        startTime.setHours(hour, minute, second, 0);
        
        return startTime.toISOString();
    }

    // Generate business-hour weighted time
    generateBusinessHour() {
        const rand = Math.random();
        
        // 70% chance of business hours (9-18)
        if (rand < 0.7) {
            return 9 + Math.floor(Math.random() * 9); // 9-17
        }
        // 20% chance of evening (18-22)
        else if (rand < 0.9) {
            return 18 + Math.floor(Math.random() * 4); // 18-21
        }
        // 10% chance of other hours
        else {
            const allHours = [0, 1, 2, 3, 4, 5, 6, 7, 8, 22, 23];
            return allHours[Math.floor(Math.random() * allHours.length)];
        }
    }

    // Calculate charge based on duration and rate
    calculateCharge(duration) {
        const baseRate = this.options.defaultRate;
        const variation = (Math.random() - 0.5) * this.options.rateVariation * 2;
        const rate = Math.max(0.001, baseRate + variation);
        
        return Math.round(duration * rate * 100) / 100; // Round to 2 decimal places
    }

    // Calculate cost (typically lower than charge)
    calculateCost(charge) {
        const costVariation = 0.1; // ±10% variation
        const variation = (Math.random() - 0.5) * costVariation * 2;
        const cost = charge * this.options.costMultiplier * (1 + variation);
        
        return Math.max(0.001, Math.round(cost * 100) / 100);
    }

    // Generate a single CDR record
    generateRecord() {
        const cdrId = this.generateCdrId();
        const sipUser = this.generateSipUser();
        const duration = this.generateDuration();
        const startTime = this.generateStartTime();
        const charge = this.calculateCharge(duration);
        const cost = this.calculateCost(charge);

        // Create raw_data object matching MagnusBilling API format
        const rawData = {
            id: cdrId,
            uniqueid: `${Date.now()}-${Math.floor(Math.random() * 10000)}`,
            accountcode: sipUser.split('@')[0],
            src: sipUser,
            dst: `+1${Math.floor(Math.random() * **********) + *********0}`,
            dcontext: 'from-internal',
            clid: `"${sipUser.split('@')[0]}" <${sipUser}>`,
            channel: `SIP/${sipUser.split('@')[0]}-${Math.floor(Math.random() * *********).toString(16)}`,
            dstchannel: '',
            lastapp: 'Dial',
            lastdata: `SIP/trunk-${Math.floor(Math.random() * 100)}`,
            starttime: startTime,
            answertime: addMinutes(new Date(startTime), Math.random() * 2).toISOString(),
            endtime: addMinutes(new Date(startTime), Math.ceil(duration / 60)).toISOString(),
            duration: duration,
            billsec: Math.max(0, duration - Math.floor(Math.random() * 5)),
            disposition: Math.random() > 0.1 ? 'ANSWERED' : 'NO ANSWER',
            amaflags: '3',
            userfield: '',
            profit: charge - cost
        };

        return {
            cdr_id: cdrId,
            sip_user: sipUser,
            duration: duration,
            start_time: startTime,
            charge: charge,
            cost: cost,
            raw_data: rawData
        };
    }

    // Generate multiple records efficiently
    generateBatch(count) {
        console.log(`Generating ${count.toLocaleString()} CDR records...`);
        const startTime = Date.now();
        
        const records = [];
        
        // Generate in chunks to avoid memory issues
        const chunkSize = 10000;
        const totalChunks = Math.ceil(count / chunkSize);
        
        for (let chunk = 0; chunk < totalChunks; chunk++) {
            const chunkStart = chunk * chunkSize;
            const chunkEnd = Math.min(chunkStart + chunkSize, count);
            const chunkCount = chunkEnd - chunkStart;
            
            console.log(`  Generating chunk ${chunk + 1}/${totalChunks} (${chunkCount.toLocaleString()} records)...`);
            
            for (let i = 0; i < chunkCount; i++) {
                records.push(this.generateRecord());
            }
            
            // Report progress for large batches
            if (count > 50000 && (chunk + 1) % 5 === 0) {
                const progress = ((chunk + 1) / totalChunks * 100).toFixed(1);
                console.log(`    Progress: ${progress}% (${records.length.toLocaleString()}/${count.toLocaleString()} records)`);
            }
        }
        
        const endTime = Date.now();
        const duration = endTime - startTime;
        const recordsPerSecond = Math.round(count / (duration / 1000));
        
        console.log(`Generated ${count.toLocaleString()} CDR records in ${duration}ms (${recordsPerSecond.toLocaleString()} records/sec)`);
        
        return records;
    }

    // Generate records with specific patterns
    generateWithPattern(count, pattern = 'mixed') {
        const originalOptions = { ...this.options };
        
        switch (pattern) {
            case 'short_calls':
                this.options.maxDuration = 60; // 1 minute max
                this.options.avgDuration = 30; // 30 seconds avg
                break;
                
            case 'long_calls':
                this.options.minDuration = 300; // 5 minutes min
                this.options.avgDuration = 900; // 15 minutes avg
                break;
                
            case 'business_hours':
                // Already default behavior
                break;
                
            case 'after_hours':
                // Override business hour generation
                this.generateBusinessHour = () => {
                    return Math.random() < 0.5 ? 
                        Math.floor(Math.random() * 8) : // 0-7 AM
                        22 + Math.floor(Math.random() * 2); // 22-23 PM
                };
                break;
                
            case 'mixed':
            default:
                // Use default settings
                break;
        }
        
        const records = this.generateBatch(count);
        
        // Restore original options
        this.options = originalOptions;
        
        return records;
    }
}

module.exports = CDRGenerator;
