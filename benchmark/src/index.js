#!/usr/bin/env node

const { program } = require('commander');
const fs = require('fs');
const path = require('path');

// Import benchmark components
const CDRGenerator = require('./generators/cdr-generator');
const MetricsCollector = require('./metrics/collector');
const ReportGenerator = require('./utils/report-generator');
const SingleInsertStrategy = require('./strategies/single-insert');
const BatchInsertStrategy = require('./strategies/batch-insert');
const TransactionInsertStrategy = require('./strategies/transaction-insert');

class BenchmarkRunner {
    constructor(options = {}) {
        this.options = {
            records: options.records || 100000,
            dbPath: options.dbPath || 'phantom_billing_benchmark.duckdb',
            strategies: options.strategies || ['batch-1000', 'batch-10000', 'transaction-10000'],
            pattern: options.pattern || 'mixed',
            export: options.export !== false,
            ...options
        };

        this.metrics = new MetricsCollector();
        this.generator = new CDRGenerator();
        this.strategies = [];
        this.generatedRecords = null;
    }

    // Parse strategy configuration
    parseStrategies(strategyNames) {
        const strategies = [];

        for (const strategyName of strategyNames) {
            try {
                if (strategyName === 'single') {
                    strategies.push(new SingleInsertStrategy(this.options.dbPath));
                } else if (strategyName.startsWith('batch-')) {
                    const batchSize = parseInt(strategyName.split('-')[1]);
                    strategies.push(new BatchInsertStrategy(this.options.dbPath, batchSize));
                } else if (strategyName.startsWith('transaction-')) {
                    const transactionSize = parseInt(strategyName.split('-')[1]);
                    strategies.push(new TransactionInsertStrategy(this.options.dbPath, transactionSize));
                } else {
                    console.warn(`Unknown strategy: ${strategyName}, skipping...`);
                }
            } catch (error) {
                console.error(`Failed to create strategy ${strategyName}:`, error.message);
            }
        }

        return strategies;
    }

    // Clean up database file before testing
    async cleanupDatabase() {
        try {
            if (fs.existsSync(this.options.dbPath)) {
                console.log(`Removing existing database: ${this.options.dbPath}`);
                fs.unlinkSync(this.options.dbPath);
            }
        } catch (error) {
            console.warn('Warning: Failed to cleanup database:', error.message);
        }
    }

    // Generate test data
    async generateTestData() {
        console.log(`\nGenerating ${this.options.records.toLocaleString()} CDR records...`);
        
        const startTime = Date.now();
        this.generatedRecords = this.generator.generateWithPattern(
            this.options.records, 
            this.options.pattern
        );
        const duration = Date.now() - startTime;

        console.log(`Data generation completed in ${duration}ms`);
        console.log(`Memory usage: ${this.formatBytes(process.memoryUsage().heapUsed)}`);
        
        return this.generatedRecords;
    }

    // Format bytes utility
    formatBytes(bytes) {
        if (bytes === 0) return '0 B';
        const k = 1024;
        const sizes = ['B', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }

    // Test a single strategy
    async testStrategy(strategy, records) {
        console.log(`\\n${'='.repeat(60)}`);
        console.log(`Testing Strategy: ${strategy.name}`);
        console.log(`Description: ${strategy.description}`);
        console.log(`${'='.repeat(60)}`);

        try {
            // Initialize strategy
            await strategy.initialize();

            // Clean existing data
            await strategy.cleanup();

            // Run insertion test
            const metrics = await strategy.insertRecords(records);

            // Get final database statistics
            const stats = strategy.getStats();
            console.log(`\\n[${strategy.name}] Final database stats:`, stats);

            // Close strategy connection
            await strategy.close();

            return metrics;
        } catch (error) {
            console.error(`[${strategy.name}] Strategy failed:`, error);
            
            try {
                await strategy.close();
            } catch (closeError) {
                console.warn(`[${strategy.name}] Failed to close strategy:`, closeError.message);
            }

            // Return error metrics
            return {
                strategy: strategy.name,
                totalRecords: records.length,
                successCount: 0,
                errorCount: records.length,
                duration: 0,
                recordsPerSecond: 0,
                memoryUsage: { before: {}, after: {}, peak: 0 },
                errors: [{ error: error.message }]
            };
        }
    }

    // Run all benchmark tests
    async runBenchmark() {
        console.log('\\n' + '='.repeat(80));
        console.log('PHANTOM BILLING DUCKDB BENCHMARK');
        console.log('='.repeat(80));
        console.log(`Records to test: ${this.options.records.toLocaleString()}`);
        console.log(`Pattern: ${this.options.pattern}`);
        console.log(`Database: ${this.options.dbPath}`);
        console.log(`Strategies: ${this.options.strategies.join(', ')}`);

        // Set up metrics collector
        this.metrics.setConfig({
            records: this.options.records,
            strategies: this.options.strategies,
            dbPath: this.options.dbPath
        });

        this.metrics.startTest();

        try {
            // Clean up any existing database
            await this.cleanupDatabase();

            // Parse and create strategy instances
            this.strategies = this.parseStrategies(this.options.strategies);
            
            if (this.strategies.length === 0) {
                throw new Error('No valid strategies configured');
            }

            // Generate test data once for all strategies
            const records = await this.generateTestData();

            // Test each strategy
            for (let i = 0; i < this.strategies.length; i++) {
                const strategy = this.strategies[i];
                
                console.log(`\\nProgress: ${i + 1}/${this.strategies.length} strategies`);
                
                // Collect system metrics before test
                this.metrics.collectSystemMetrics();
                
                // Test the strategy
                const strategyMetrics = await this.testStrategy(strategy, records);
                
                // Collect results
                this.metrics.addStrategyMetrics(strategyMetrics);
                
                // Collect system metrics after test
                this.metrics.collectSystemMetrics();

                // Brief delay between tests
                if (i < this.strategies.length - 1) {
                    console.log('Waiting 2 seconds before next test...');
                    await new Promise(resolve => setTimeout(resolve, 2000));
                }
            }

            this.metrics.endTest();

            // Print summary
            this.metrics.printSummary();

            // Export results if enabled
            if (this.options.export) {
                await this.exportResults();
            }

            return this.metrics.getSummary();

        } catch (error) {
            console.error('Benchmark failed:', error);
            this.metrics.endTest();
            throw error;
        } finally {
            // Cleanup
            await this.cleanup();
        }
    }

    // Export benchmark results
    async exportResults() {
        console.log('\\nExporting results...');
        
        try {
            const csvPath = await this.metrics.exportToCSV();
            const jsonPath = await this.metrics.exportToJSON();
            
            // Generate detailed Markdown report
            const reportGenerator = new ReportGenerator(this.metrics);
            const reportPath = await reportGenerator.saveReport();
            
            console.log(`Results exported:`);
            console.log(`  CSV: ${csvPath}`);
            console.log(`  JSON: ${jsonPath}`);
            console.log(`  Report: ${reportPath}`);
        } catch (error) {
            console.error('Failed to export results:', error);
        }
    }

    // Cleanup resources
    async cleanup() {
        // Close any remaining strategy connections
        for (const strategy of this.strategies) {
            try {
                await strategy.close();
            } catch (error) {
                // Ignore cleanup errors
            }
        }

        // Free memory
        this.generatedRecords = null;
        
        if (global.gc) {
            global.gc();
        }
    }
}

// CLI interface
async function main() {
    program
        .name('phantom-billing-benchmark')
        .description('DuckDB CDR processing performance benchmark')
        .version('1.0.0');

    program
        .option('-r, --records <number>', 'Number of CDR records to generate and test', '100000')
        .option('-s, --strategies <strategies...>', 'Insertion strategies to test', ['batch-1000', 'batch-10000', 'transaction-10000'])
        .option('-d, --db-path <path>', 'Database file path', 'phantom_billing_benchmark.duckdb')
        .option('-p, --pattern <pattern>', 'CDR generation pattern', 'mixed')
        .option('--no-export', 'Disable result export')
        .option('--quick', 'Quick test with 10,000 records')
        .option('--full', 'Full test with 100,000 records')
        .option('--all-strategies', 'Test all available strategies');

    program.parse();
    const options = program.opts();

    // Handle convenience flags
    if (options.quick) {
        options.records = '10000';
    } else if (options.full) {
        options.records = '100000';
    }

    if (options.allStrategies) {
        options.strategies = [
            'single',
            'batch-100', 'batch-1000', 'batch-10000',
            'transaction-1000', 'transaction-10000'
        ];
    }

    // Parse numeric options
    const parsedOptions = {
        ...options,
        records: parseInt(options.records),
        export: !options.noExport
    };

    // Validate options
    if (parsedOptions.records < 1 || parsedOptions.records > 1000000) {
        console.error('Error: Records must be between 1 and 1,000,000');
        process.exit(1);
    }

    console.log('Starting benchmark with configuration:');
    console.log('  Records:', parsedOptions.records.toLocaleString());
    console.log('  Strategies:', parsedOptions.strategies);
    console.log('  Pattern:', parsedOptions.pattern);
    console.log('  Database:', parsedOptions.dbPath);
    console.log('  Export:', parsedOptions.export);

    try {
        const benchmark = new BenchmarkRunner(parsedOptions);
        const results = await benchmark.runBenchmark();
        
        console.log('\\nBenchmark completed successfully!');
        
        if (results.recommendations) {
            console.log('\\nKey Recommendations:');
            results.recommendations.forEach(rec => {
                console.log(`• ${rec.message}`);
            });
        }
        
        process.exit(0);
    } catch (error) {
        console.error('\\nBenchmark failed:', error.message);
        process.exit(1);
    }
}

// Run if called directly
if (require.main === module) {
    main().catch(error => {
        console.error('Fatal error:', error);
        process.exit(1);
    });
}

module.exports = BenchmarkRunner;
