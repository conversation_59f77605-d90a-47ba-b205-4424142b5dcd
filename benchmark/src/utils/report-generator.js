const fs = require('fs');
const path = require('path');
const { format } = require('date-fns');

class ReportGenerator {
    constructor(metrics) {
        this.metrics = metrics;
        this.summary = metrics.getSummary();
    }

    // Format bytes to human readable
    formatBytes(bytes) {
        if (bytes === 0) return '0 B';
        const k = 1024;
        const sizes = ['B', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(Math.abs(bytes)) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }

    // Format duration to human readable
    formatDuration(ms) {
        if (ms < 1000) return `${ms}ms`;
        if (ms < 60000) return `${(ms / 1000).toFixed(2)}s`;
        return `${(ms / 60000).toFixed(2)}m`;
    }

    // Generate executive summary
    generateExecutiveSummary() {
        const comp = this.summary.performanceComparison;
        
        return `## Executive Summary

This benchmark tested DuckDB insertion performance using ${this.summary.totalRecordsProcessed?.toLocaleString()} CDR (Call Detail Records) across ${this.summary.strategiesTesteed} different insertion strategies.

### Key Findings

${comp ? `
- **Best Performing Strategy**: ${comp.fastest.strategy} achieved ${comp.fastest.recordsPerSecond?.toLocaleString()} records/second
- **Performance Range**: ${comp.speedupRatio?.toFixed(1)}x difference between fastest and slowest strategies
- **Recommended Approach**: ${comp.fastest.strategy} for optimal throughput
` : '- No comparative data available'}

### Test Configuration

- **Total Records**: ${this.summary.totalRecordsProcessed?.toLocaleString()}
- **Test Duration**: ${this.summary.totalTestDuration}
- **Platform**: ${this.summary.testConfiguration?.environment?.platform} ${this.summary.testConfiguration?.environment?.arch}
- **Node Version**: ${this.summary.testConfiguration?.environment?.nodeVersion}
`;
    }

    // Generate detailed results table
    generateResultsTable() {
        if (!this.metrics.metrics || this.metrics.metrics.length === 0) {
            return `## Detailed Results

No results available.`;
        }

        const headers = ['Strategy', 'Records/sec', 'Duration', 'Success Rate', 'Memory Peak', 'DB Size'];
        const rows = this.metrics.metrics.map(m => [
            m.strategy,
            m.recordsPerSecond?.toLocaleString() || 'N/A',
            this.formatDuration(m.duration),
            `${((m.successCount / (m.totalRecords || 1)) * 100).toFixed(1)}%`,
            this.formatBytes(m.efficiency.memoryEfficiency),
            this.formatBytes(m.databaseSize)
        ]);

        const table = [
            `| ${headers.join(' | ')} |`,
            `| ${headers.map(() => '---').join(' | ')} |`,
            ...rows.map(row => `| ${row.join(' | ')} |`)
        ].join('\n');

        return `## Detailed Results

${table}

### Performance Metrics Explained

- **Records/sec**: Number of CDR records processed per second (higher is better)
- **Duration**: Total time taken to process all records
- **Success Rate**: Percentage of records successfully inserted
- **Memory Peak**: Peak memory usage during insertion
- **DB Size**: Final database file size after insertion
`;
    }

    // Generate performance comparison chart (text-based)
    generatePerformanceChart() {
        if (!this.metrics.metrics || this.metrics.metrics.length === 0) {
            return '';
        }

        const maxThroughput = Math.max(...this.metrics.metrics.map(m => m.recordsPerSecond || 0));
        const chartWidth = 50;

        const chart = this.metrics.metrics.map(m => {
            const throughput = m.recordsPerSecond || 0;
            const barLength = Math.round((throughput / maxThroughput) * chartWidth);
            const bar = '█'.repeat(barLength) + '░'.repeat(chartWidth - barLength);
            return `${m.strategy.padEnd(20)} |${bar}| ${throughput.toLocaleString()} rec/sec`;
        }).join('\n');

        return `## Performance Comparison

\`\`\`
${chart}
\`\`\`
`;
    }

    // Generate recommendations section
    generateRecommendations() {
        if (!this.summary.recommendations || this.summary.recommendations.length === 0) {
            return `## Recommendations

No specific recommendations available based on current test results.`;
        }

        const recommendations = this.summary.recommendations.map((rec, index) => 
            `${index + 1}. **${rec.type.charAt(0).toUpperCase() + rec.type.slice(1)}**: ${rec.message}`
        ).join('\n');

        return `## Recommendations

${recommendations}

### General Guidelines

- For **high-throughput scenarios**: Use the fastest strategy identified above
- For **memory-constrained environments**: Consider the most memory-efficient option
- For **reliability-critical applications**: Prefer strategies with low error rates
- **Batch sizes**: Larger batches generally perform better but consume more memory
- **Transactions**: Provide ACID guarantees but may reduce throughput
`;
    }

    // Generate technical details section
    generateTechnicalDetails() {
        return `## Technical Details

### Test Environment

- **Platform**: ${this.summary.testConfiguration?.environment?.platform}
- **Architecture**: ${this.summary.testConfiguration?.environment?.arch}
- **Node.js Version**: ${this.summary.testConfiguration?.environment?.nodeVersion}
- **Memory Limit**: ${this.formatBytes(this.summary.testConfiguration?.environment?.memoryLimit || 0)}

### CDR Record Schema

Each CDR record contains the following fields:
- \`cdr_id\`: Unique identifier for the call
- \`sip_user\`: SIP user making the call
- \`duration\`: Call duration in seconds
- \`charge\`: Calculated charge for the call
- \`cost\`: Provider cost for the call
- \`start_time\`: Call start timestamp
- \`raw_data\`: Complete MagnusBilling API response

### Database Schema

\`\`\`sql
CREATE TABLE cdr_processing (
    cdr_id VARCHAR PRIMARY KEY,
    sip_user VARCHAR NOT NULL,
    duration DOUBLE NOT NULL,
    charge DOUBLE NOT NULL,
    cost DOUBLE DEFAULT 0,
    start_time TIMESTAMP NOT NULL,
    processed_at TIMESTAMP,
    synced_to_supabase BOOLEAN DEFAULT FALSE,
    raw_data VARCHAR
);

-- Indexes for performance
CREATE INDEX idx_cdr_id ON cdr_processing(cdr_id);
CREATE INDEX idx_cdr_synced ON cdr_processing(synced_to_supabase);
CREATE INDEX idx_cdr_processed_at ON cdr_processing(processed_at);
\`\`\`

### Insertion Strategies

1. **Single Insert**: Individual INSERT statements for each record
2. **Batch Insert**: Multi-value INSERT statements with configurable batch sizes
3. **Transaction Insert**: Batched INSERTs wrapped in transactions for ACID compliance
`;
    }

    // Generate memory usage section
    generateMemoryAnalysis() {
        if (!this.metrics.metrics || this.metrics.metrics.length === 0) {
            return '';
        }

        const memoryData = this.metrics.metrics.map(m => ({
            strategy: m.strategy,
            peak: m.efficiency.memoryEfficiency,
            efficiency: (m.recordsPerSecond || 0) / (m.efficiency.memoryEfficiency / 1024 / 1024) // records per MB
        }));

        const table = [
            '| Strategy | Memory Peak | Efficiency (rec/MB) |',
            '| --- | --- | --- |',
            ...memoryData.map(m => `| ${m.strategy} | ${this.formatBytes(m.peak)} | ${m.efficiency.toFixed(0)} |`)
        ].join('\n');

        return `## Memory Usage Analysis

${table}

**Memory Efficiency** is calculated as records processed per MB of peak memory usage. Higher values indicate better memory utilization.
`;
    }

    // Generate full Markdown report
    generateFullReport() {
        const timestamp = format(new Date(), 'MMMM d, yyyy \'at\' h:mm a');
        
        return `# DuckDB CDR Processing Performance Benchmark

**Generated**: ${timestamp}

${this.generateExecutiveSummary()}

${this.generateResultsTable()}

${this.generatePerformanceChart()}

${this.generateMemoryAnalysis()}

${this.generateRecommendations()}

${this.generateTechnicalDetails()}

---

## Appendix

### Raw Test Data

For detailed analysis, see the accompanying JSON and CSV files:
- \`benchmark_metrics_*.json\` - Complete test metrics and system data
- \`benchmark_results_*.csv\` - Tabular data suitable for spreadsheet analysis

### Reproducing Results

To reproduce these results, run:

\`\`\`bash
# Quick test (10k records)
bun benchmark --quick

# Full test (100k records) 
bun benchmark --full

# Custom configuration
bun benchmark --records 50000 --strategies batch-1000 batch-5000 transaction-10000
\`\`\`

### Notes

- Results may vary based on system specifications and current load
- DuckDB performance is highly dependent on available memory and storage type
- For production use, test with realistic data volumes and patterns
- Consider implementing connection pooling for high-concurrency scenarios

*Benchmark generated by Phantom Billing Service Performance Suite*
`;
    }

    // Save report to file
    async saveReport(filename = null) {
        if (!filename) {
            filename = `benchmark_report_${format(new Date(), 'yyyyMMdd_HHmmss')}.md`;
        }

        const reportPath = path.join('results', filename);
        
        // Ensure results directory exists
        const resultsDir = path.dirname(reportPath);
        if (!fs.existsSync(resultsDir)) {
            fs.mkdirSync(resultsDir, { recursive: true });
        }

        const reportContent = this.generateFullReport();
        
        fs.writeFileSync(reportPath, reportContent);
        console.log(`[Report] Detailed report saved to: ${reportPath}`);
        
        return reportPath;
    }
}

module.exports = ReportGenerator;
