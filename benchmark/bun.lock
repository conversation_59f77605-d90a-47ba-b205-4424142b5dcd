{
  "lockfileVersion": 1,
  "workspaces": {
    "": {
      "name": "phantom-billing-benchmark",
      "dependencies": {
        "@evan/duckdb": "^0.1.5",
        "commander": "^11.1.0",
        "csv-writer": "^1.6.0",
        "date-fns": "^2.30.0",
      },
    },
  },
  "packages": {
    "@babel/runtime": ["@babel/runtime@7.28.4", "", {}, "sha512-Q/N6JNWvIvPnLDvjlE1OUBLPQHH6l3CltCEsHIujp45zQUSSh8K+gHnaEX45yAT1nyngnINhvWtzN+Nb9D8RAQ=="],

    "@evan/duckdb": ["@evan/duckdb@0.1.5", "", {}, "sha512-D93SMCZHqE2XPbrIxQx2/UpppkIi2xL0LX3xqLTqt8QgXLORbFJiP+Kzb815AIQ8eNQ9ng0LwQkEd2aJDR1uBA=="],

    "commander": ["commander@11.1.0", "", {}, "sha512-yPVavfyCcRhmorC7rWlkHn15b4wDVgVmBA7kV4QVBsF7kv/9TKJAbAXVTxvTnwP8HHKjRCJDClKbciiYS7p0DQ=="],

    "csv-writer": ["csv-writer@1.6.0", "", {}, "sha512-NOx7YDFWEsM/fTRAJjRpPp8t+MKRVvniAg9wQlUKx20MFrPs73WLJhFf5iteqrxNYnsy924K3Iroh3yNHeYd2g=="],

    "date-fns": ["date-fns@2.30.0", "", { "dependencies": { "@babel/runtime": "^7.21.0" } }, "sha512-fnULvOpxnC5/Vg3NCiWelDsLiUc9bRwAPs/+LfTLNvetFCtCTN+yQz15C/fs4AwX1R9K5GLtLfn8QW+dWisaAw=="],
  }
}
