# DuckDB CDR Processing Performance Benchmark

A comprehensive performance benchmarking suite for testing DuckDB insertion strategies with realistic CDR (Call Detail Record) data. This benchmark helps optimize database insertion performance for high-throughput billing and telephony systems.

## Overview

This benchmark suite tests various DuckDB insertion strategies by:

- Generating realistic CDR records with proper data distributions
- Testing multiple insertion approaches (single, batch, transaction)
- Measuring performance metrics (throughput, memory, errors)
- Providing detailed analysis and recommendations

## Quick Start

### Prerequisites

- **Bun** >= 1.0.0 (recommended) or **Node.js** >= 18.0.0
- **DuckDB** (installed via npm dependencies)
- **macOS** or **Linux** (Windows support not tested)

### Installation

```bash
# Navigate to benchmark directory
cd benchmark

# Install dependencies
bun install

# Make benchmark script executable
chmod +x src/index.js
```

### Running Benchmarks

```bash
# Quick test with 10,000 records
bun benchmark --quick

# Full test with 100,000 records (recommended)
bun benchmark --full

# Custom configuration
bun benchmark --records 50000 --strategies batch-1000 batch-5000 transaction-10000

# Test all available strategies
bun benchmark --all-strategies --records 25000
```

## Usage

### Basic Commands

```bash
# Default benchmark (100k records, standard strategies)
bun benchmark

# Quick performance test
bun benchmark --quick

# Full comprehensive test
bun benchmark --full

# Clean up generated files
bun clean
```

### Advanced Configuration

```bash
# Custom record count and strategies
bun benchmark \
  --records 75000 \
  --strategies batch-1000 batch-10000 transaction-5000 \
  --pattern business_hours \
  --db-path custom_test.duckdb

# Disable result export
bun benchmark --records 10000 --no-export

# Test specific data patterns
bun benchmark --pattern short_calls --records 20000
bun benchmark --pattern long_calls --records 15000
```

## Configuration Options

| Option | Description | Default | Example |
|--------|-------------|---------|---------|
| `--records <number>` | Number of CDR records to generate | 100000 | `--records 50000` |
| `--strategies <list>` | Insertion strategies to test | `batch-1000 batch-10000 transaction-10000` | `--strategies single batch-1000` |
| `--db-path <path>` | Database file path | `phantom_billing_benchmark.duckdb` | `--db-path test.db` |
| `--pattern <type>` | CDR data generation pattern | `mixed` | `--pattern business_hours` |
| `--no-export` | Disable CSV/JSON export | false | `--no-export` |
| `--quick` | Quick test (10k records) | - | `--quick` |
| `--full` | Full test (100k records) | - | `--full` |
| `--all-strategies` | Test all available strategies | - | `--all-strategies` |

### Available Strategies

| Strategy | Description | Best For |
|----------|-------------|----------|
| `single` | Individual INSERT statements | Small datasets, debugging |
| `batch-<size>` | Multi-value INSERT statements | General use, balanced performance |
| `transaction-<size>` | Batch inserts in transactions | ACID requirements, consistency |

**Batch sizes**: 100, 1000, 5000, 10000  
**Transaction sizes**: 1000, 5000, 10000, 25000

### Data Patterns

| Pattern | Description | Use Case |
|---------|-------------|----------|
| `mixed` | Realistic mix of call patterns | General testing |
| `business_hours` | Calls during 9-5 business hours | Office environments |
| `after_hours` | Calls outside business hours | International/24h operations |
| `short_calls` | Calls under 60 seconds | SMS, quick calls |
| `long_calls` | Calls over 5 minutes | Conference calls, support |

## Output Files

The benchmark generates several output files in the `results/` directory:

### CSV Results
```
benchmark_results_YYYYMMDD_HHMMSS.csv
```
Tabular data suitable for spreadsheet analysis with columns:
- Strategy name
- Total records processed
- Success/error counts
- Duration and throughput metrics
- Memory usage statistics

### JSON Metrics
```
benchmark_metrics_YYYYMMDD_HHMMSS.json
```
Complete test data including:
- Detailed performance metrics
- System resource usage
- Error details and diagnostics
- Test configuration

### Markdown Report
```
benchmark_report_YYYYMMDD_HHMMSS.md
```
Human-readable analysis including:
- Executive summary
- Performance comparison charts
- Recommendations
- Technical details

## Understanding Results

### Key Metrics

- **Records/second**: Insertion throughput (higher is better)
- **Success Rate**: Percentage of successful insertions
- **Memory Peak**: Maximum memory usage during test
- **Error Rate**: Percentage of failed insertions
- **Database Size**: Final size of DuckDB file

### Performance Interpretation

**Excellent**: > 50,000 records/second  
**Good**: 20,000 - 50,000 records/second  
**Fair**: 5,000 - 20,000 records/second  
**Poor**: < 5,000 records/second

### Sample Output

```
BENCHMARK SUMMARY
================================================================================
Total Records Tested: 100,000
Strategies Tested: 3
Total Test Duration: 12.34s

Fastest: Batch Insert (10000) (87,234 rec/sec)
Slowest: Single Insert (1,456 rec/sec)
Speed Difference: 59.9x faster

Detailed Results:
1. Batch Insert (10000):
   Throughput: 87,234 records/second
   Duration: 1.15s
   Success Rate: 100.0%
   Memory Peak: 45.2 MB

2. Transaction Insert (10000):
   Throughput: 78,901 records/second
   Duration: 1.27s
   Success Rate: 100.0%
   Memory Peak: 52.1 MB

3. Single Insert:
   Throughput: 1,456 records/second
   Duration: 68.7s
   Success Rate: 99.9%
   Memory Peak: 8.3 MB
```

## Architecture

### Project Structure

```
benchmark/
├── src/
│   ├── generators/
│   │   └── cdr-generator.js       # CDR data generation
│   ├── strategies/
│   │   ├── single-insert.js       # Individual INSERT statements
│   │   ├── batch-insert.js        # Multi-value INSERTs
│   │   └── transaction-insert.js  # Transaction-wrapped batches
│   ├── metrics/
│   │   └── collector.js           # Performance measurement
│   ├── utils/
│   │   └── report-generator.js    # Report generation
│   └── index.js                   # Main benchmark runner
├── results/                       # Output files
├── package.json
└── README.md
```

### CDR Record Schema

Each generated CDR contains:

```javascript
{
  cdr_id: "cdr_abc123_def456_789",      // Unique identifier
  sip_user: "<EMAIL>",     // SIP user
  duration: 180,                         // Call duration (seconds)
  start_time: "2024-01-15T14:30:00Z",   // ISO timestamp
  charge: 1.80,                         // Calculated charge
  cost: 1.44,                           // Provider cost
  raw_data: { /* MagnusBilling format */ }  // Complete API response
}
```

### Database Schema

```sql
CREATE TABLE cdr_processing (
    cdr_id VARCHAR PRIMARY KEY,
    sip_user VARCHAR NOT NULL,
    duration DOUBLE NOT NULL,
    charge DOUBLE NOT NULL,
    cost DOUBLE DEFAULT 0,
    start_time TIMESTAMP NOT NULL,
    processed_at TIMESTAMP,
    synced_to_supabase BOOLEAN DEFAULT FALSE,
    raw_data VARCHAR
);

-- Performance indexes
CREATE INDEX idx_cdr_id ON cdr_processing(cdr_id);
CREATE INDEX idx_cdr_synced ON cdr_processing(synced_to_supabase);
CREATE INDEX idx_cdr_processed_at ON cdr_processing(processed_at);
```

## Troubleshooting

### Common Issues

**Error: "IO Error: Trying to read a database file with version number X"**
```bash
# Clean existing database files
bun clean
rm -f *.duckdb
```

**Error: "out of memory"**
```bash
# Reduce record count or batch size
bun benchmark --records 10000 --strategies batch-1000
```

**Error: "Module not found: @evan/duckdb"**
```bash
# Reinstall dependencies
rm -rf node_modules package-lock.json
bun install
```

### Performance Issues

**Slow performance on macOS**
- Ensure sufficient free disk space (> 2GB)
- Close memory-intensive applications
- Use SSD storage for database files

**High memory usage**
- Reduce batch sizes (use batch-1000 instead of batch-10000)
- Lower record count for initial testing
- Enable garbage collection: `node --expose-gc src/index.js`

### Debug Mode

```bash
# Enable detailed logging
DEBUG=1 bun benchmark --records 1000

# Test with minimal configuration
bun benchmark --quick --strategies batch-100 --no-export
```

## Development

### Adding New Strategies

1. Create new strategy file in `src/strategies/`
2. Implement required methods: `initialize()`, `insertRecords()`, `cleanup()`, `close()`
3. Update `src/index.js` to recognize new strategy pattern
4. Test with small dataset first

### Contributing

1. Fork the repository
2. Create feature branch
3. Add tests for new functionality
4. Update documentation
5. Submit pull request

### Testing

```bash
# Test generator only
node -e "const gen = require('./src/generators/cdr-generator'); console.log(gen.generateRecord());"

# Test specific strategy
bun benchmark --records 100 --strategies batch-100 --no-export

# Validate database schema
sqlite3 phantom_billing_benchmark.duckdb ".schema cdr_processing"
```

## FAQ

**Q: Why use DuckDB instead of PostgreSQL/MySQL?**  
A: DuckDB is an embedded analytical database optimized for OLAP workloads, making it ideal for CDR processing and analytics without requiring a separate server.

**Q: Can this benchmark be used for production capacity planning?**  
A: Yes, but test with your actual data patterns and hardware. Results vary significantly based on CPU, memory, and storage performance.

**Q: What's the difference between batch and transaction strategies?**  
A: Batch strategies focus on throughput, while transaction strategies provide ACID guarantees at the cost of some performance.

**Q: How do I benchmark with my own CDR format?**  
A: Modify `src/generators/cdr-generator.js` to match your CDR schema and data patterns.

**Q: Can I run benchmarks in production?**  
A: Not recommended. This benchmark creates significant system load and should only be run in development/testing environments.

## License

This benchmark suite is part of the Phantom Billing Service and is intended for performance testing and optimization.

---

For questions or issues, please refer to the main Phantom Billing Service documentation or create an issue in the project repository.
