{"name": "phantom-billing-benchmark", "version": "1.0.0", "description": "Performance benchmark for DuckDB CDR processing", "private": true, "main": "src/index.js", "scripts": {"benchmark": "bun src/index.js", "benchmark:quick": "bun src/index.js --records=10000", "benchmark:full": "bun src/index.js --records=100000", "clean": "rm -rf results/* phantom_billing_benchmark.duckdb"}, "dependencies": {"@evan/duckdb": "^0.1.5", "commander": "^11.1.0", "csv-writer": "^1.6.0", "date-fns": "^2.30.0"}, "engines": {"bun": ">=1.0.0"}}