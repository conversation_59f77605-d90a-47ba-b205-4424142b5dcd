{"name": "phantom-billing-service", "version": "1.0.0", "description": "High-performance CDR processing service for MagnusBilling API", "private": true, "main": "src/cdr_processor.js", "scripts": {"start": "bun src/cdr_processor.js", "dev": "LOG_LEVEL=debug bun src/cdr_processor.js", "build": "bun build src/cdr_processor.js --outdir build --target bun --minify && cp ecosystem.config.cjs build/ && cp .env build/.env", "build:prod": "bun build src/cdr_processor.js --outdir build --target bun --minify --splitting --external @evan/duckdb --external @supabase/supabase-js", "prod": "NODE_ENV=production bun build/cdr_processor.js", "clean": "rm -rf build", "prebuild": "npm run clean", "test-config": "bun -e \"const {validateConfig, logConfig} = require('./src/config.js'); validateConfig(); logConfig();\"", "pm2:start": "pm2 start ecosystem.config.js --only phantom-billing-service", "pm2:start:dev": "pm2 start ecosystem.config.js --only phantom-billing-service-dev", "pm2:stop": "pm2 stop phantom-billing-service", "pm2:stop:dev": "pm2 stop phantom-billing-service-dev", "pm2:restart": "pm2 restart phantom-billing-service", "pm2:restart:dev": "pm2 restart phantom-billing-service-dev", "pm2:delete": "pm2 delete phantom-billing-service", "pm2:delete:dev": "pm2 delete phantom-billing-service-dev", "pm2:logs": "pm2 logs phantom-billing-service", "pm2:logs:dev": "pm2 logs phantom-billing-service-dev", "pm2:status": "pm2 status", "pm2:monit": "pm2 monit", "deploy": "npm run build:prod && npm run pm2:restart"}, "dependencies": {"@evan/duckdb": "^0.1.5", "@supabase/supabase-js": "^2.56.1"}, "engines": {"bun": ">=1.0.0"}, "os": ["linux", "darwin"], "keywords": ["billing", "cdr", "magnusbilling", "supabase", "microservice"]}