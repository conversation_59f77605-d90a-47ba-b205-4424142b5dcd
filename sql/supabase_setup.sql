-- Phantom Billing Service - Supabase Database Setup
-- This file contains the complete database schema and stored procedures

-- Create call_charges table for CDR billing records
CREATE TABLE IF NOT EXISTS call_charges (
    cdr_id TEXT PRIMARY KEY,
    sip_user TEXT NOT NULL,
    duration FLOAT NOT NULL,
    charge FLOAT NOT NULL,
    cost NUMERIC DEFAULT 0,
    processed BOOLEAN DEFAULT false,
    start_time TIMESTAMP NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Create indexes for efficient queries
CREATE INDEX IF NOT EXISTS idx_call_charges_created_at ON call_charges(created_at);
CREATE INDEX IF NOT EXISTS idx_call_charges_sip_user ON call_charges(sip_user);
CREATE INDEX IF NOT EXISTS idx_call_charges_processed ON call_charges(processed);

-- Grant necessary permissions (adjust based on your Supabase setup)
-- These may not be needed if using service role key
-- GRANT SELECT, INSERT, UPDATE, DELETE ON call_charges TO authenticated;
