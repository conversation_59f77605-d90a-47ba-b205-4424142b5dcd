// Configuration management for Phantom Billing Service
// Loads and validates environment variables

const path = require('path');

// Bun automatically loads .env, so no need for dotenv
const config = {
    // MagnusBilling API Configuration
    magnus: {
        apiUrl: process.env.API_URL || 'http://5.187.2.47/mbilling/index.php/call/read',
        apiKey: process.env.API_KEY,
        apiSecret: process.env.API_SECRET,
        maxRetries: parseInt(process.env.MAX_RETRIES) || 3,
        retryBaseDelay: parseInt(process.env.RETRY_BASE_DELAY) || 10000 // 10 seconds
    },

    // Supabase Configuration
    supabase: {
        url: process.env.SUPABASE_URL,
        key: process.env.SUPABASE_SERVICE_KEY
    },

    // Processing Configuration
    processing: {
        pollInterval: parseInt(process.env.POLL_INTERVAL) || 300000, // 5 minutes
        batchSize: parseInt(process.env.BATCH_SIZE) || 1000,
        rotationMonths: parseInt(process.env.ROTATION_MONTHS) || 3,
        cleanupInterval: parseInt(process.env.CLEANUP_INTERVAL) || 86400000, // 24 hours
        defaultRate: parseFloat(process.env.DEFAULT_RATE) || 0.01 // per second
    },

    // Database Configuration
    database: {
        // Use persistent DuckDB file to maintain sync state across restarts
        // Use absolute path to ensure it works from both src/ and build/ directories
        statePath: process.env.STATE_DB_PATH || path.join(process.cwd(), 'phantom_billing.duckdb')
    },

    // Logging Configuration
    logging: {
        level: process.env.LOG_LEVEL || 'info',
        metricsInterval: parseInt(process.env.METRICS_INTERVAL) || 60000 // 1 minute
    }
};

// Validation function
function validateConfig() {
    const errors = [];

    // Required MagnusBilling API credentials
    if (!config.magnus.apiKey) {
        errors.push('API_KEY environment variable is required');
    }
    if (!config.magnus.apiSecret) {
        errors.push('API_SECRET environment variable is required');
    }

    // Required Supabase credentials
    if (!config.supabase.url) {
        errors.push('SUPABASE_URL environment variable is required');
    }
    if (!config.supabase.key) {
        errors.push('SUPABASE_SERVICE_KEY environment variable is required');
    }

    // Validate numeric values
    if (config.processing.pollInterval < 1000) {
        errors.push('POLL_INTERVAL must be at least 1000ms (1 second)');
    }
    if (config.processing.batchSize < 1 || config.processing.batchSize > 10000) {
        errors.push('BATCH_SIZE must be between 1 and 10000');
    }
    if (config.processing.rotationMonths < 1) {
        errors.push('ROTATION_MONTHS must be at least 1');
    }
    if (config.processing.defaultRate <= 0) {
        errors.push('DEFAULT_RATE must be greater than 0');
    }

    if (errors.length > 0) {
        console.error('Configuration validation failed:');
        errors.forEach(error => console.error(`  - ${error}`));
        process.exit(1);
    }

    console.log('Configuration validation passed');
    return true;
}

// Log configuration (without secrets)
function logConfig() {
    console.log('Configuration loaded:');
    console.log('  MagnusBilling API:', config.magnus.apiUrl);
    console.log('  API Key:', config.magnus.apiKey ? '***configured***' : 'NOT SET');
    console.log('  API Secret:', config.magnus.apiSecret ? '***configured***' : 'NOT SET');
    console.log('  Supabase URL:', config.supabase.url || 'NOT SET');
    console.log('  Supabase Key:', config.supabase.key ? '***configured***' : 'NOT SET');
    console.log('  Poll Interval:', `${config.processing.pollInterval}ms`);
    console.log('  Batch Size:', config.processing.batchSize);
    console.log('  Rotation Months:', config.processing.rotationMonths);
    console.log('  Default Rate:', config.processing.defaultRate);
    console.log('  State DB:', config.database.statePath === ':memory:' ? 'In-Memory DuckDB' : `Persistent DuckDB: ${config.database.statePath}`);
}

module.exports = {
    config,
    validateConfig,
    logConfig
};
