#!/usr/bin/env bun

// Phantom Billing Service - Main CDR Processor
// Processes Call Detail Records from MagnusBilling API

const { config, validateConfig, logConfig } = require('./config.js');
const StateClient = require('./lib/duckdb_client.js');
const MagnusBillingClient = require('./lib/magnus_client.js');
const SupabaseClient = require('./lib/supabase_client.js');
const Logger = require('./lib/logger.js');
const fs = require('fs');
const path = require('path');

class CDRProcessor {
    constructor() {
        // Initialize configuration
        validateConfig();
        
        // Initialize logger
        this.logger = new Logger(config.logging.level);
        
        // Initialize clients
        this.stateClient = new StateClient(config.database.statePath);
        this.magnusClient = new MagnusBillingClient(
            config.magnus.apiUrl,
            config.magnus.apiKey,
            config.magnus.apiSecret
        );
        this.supabaseClient = new SupabaseClient(
            config.supabase.url,
            config.supabase.key
        );
        
        this.isRunning = false;
        this.shouldStop = false;
        
        // Setup graceful shutdown
        this.setupGracefulShutdown();
    }

    // Setup graceful shutdown handlers
    setupGracefulShutdown() {
        const shutdown = async (signal) => {
            this.logger.info(`Received ${signal}, shutting down gracefully...`);
            this.shouldStop = true;
            
            // Wait for current processing to complete
            while (this.isRunning) {
                await this.sleep(100);
            }
            
            await this.stateClient.close();
            this.logger.logShutdown();
            process.exit(0);
        };

        process.on('SIGTERM', () => shutdown('SIGTERM'));
        process.on('SIGINT', () => shutdown('SIGINT'));
    }

    // Sleep utility
    async sleep(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }

    // Chunk array into smaller arrays
    chunkArray(array, chunkSize) {
        const chunks = [];
        for (let i = 0; i < array.length; i += chunkSize) {
            chunks.push(array.slice(i, i + chunkSize));
        }
        return chunks;
    }

    // Process a single batch of CDRs using DuckDB for local processing
    async processCDRBatch(cdrs) {
        if (!cdrs || cdrs.length === 0) {
            return { processedCount: 0, skippedCount: 0 };
        }

        const startTime = Date.now();
        let processedCount = 0;
        let skippedCount = 0;

        try {
            // Step 1: Check for existing CDRs in local DuckDB ONLY (ultra-fast, no external calls)
            const cdrIds = cdrs.map(cdr => cdr.id).filter(id => id);
            const existingInDuckDB = this.stateClient.checkExistingCDRs(cdrIds);
            const existingSet = new Set(existingInDuckDB);

            this.logger.debug(`Found ${existingInDuckDB.length} existing CDRs out of ${cdrIds.length} in local DuckDB`);

            // Step 2: Process only NEW CDRs (not in DuckDB yet)
            const newCdrs = cdrs.filter(cdr => !existingSet.has(cdr.id));

            if (newCdrs.length === 0) {
                this.logger.info('No new CDRs to process - all already exist in local DuckDB');
                return { processedCount: 0, skippedCount: cdrs.length };
            }

            this.logger.info(`Processing ${newCdrs.length} new CDRs (skipping ${existingInDuckDB.length} existing)`);
            skippedCount = existingInDuckDB.length;

            // Step 4: Process and validate CDRs locally (no external database calls)
            const charges = [];
            for (const cdr of newCdrs) {
                try {
                    const processedCdr = this.magnusClient.processCDR(cdr);
                    if (processedCdr) {
                        const charge = this.magnusClient.calculateCharge(
                            processedCdr.duration,
                            config.processing.defaultRate
                        );
                        const cost = this.magnusClient.calculateCost(cdr);

                        charges.push({
                            cdr_id: processedCdr.cdr_id,
                            sip_user: processedCdr.sip_user,
                            duration: processedCdr.duration,
                            charge: charge,
                            cost: cost,
                            start_time: processedCdr.start_time,
                            raw_data: cdr // Keep original data
                        });
                    } else {
                        skippedCount++;
                        this.logger.debug(`Skipped invalid CDR: ${cdr.id}`);
                    }
                } catch (error) {
                    skippedCount++;
                    this.logger.warn(`Warning: Failed to process CDR ${cdr.id}:`, { error: error.message });
                }
            }

            // Step 5: Store processed charges in local DuckDB (ultra-fast)
            if (charges.length > 0) {
                const result = this.stateClient.processCDRBatch(charges);
                processedCount = result.successCount;
                skippedCount += result.errorCount;

                if (result.errors && result.errors.length > 0) {
                    this.logger.warn(`Local processing had ${result.errors.length} errors`);
                    result.errors.forEach(err => {
                        this.logger.error(`CDR ${err.cdr_id} error`, { error: err.error });
                    });
                }
            }

            const duration = Date.now() - startTime;
            this.logger.debug(`Local CDR processing completed in ${duration}ms - processed: ${processedCount}, skipped: ${skippedCount}`);

            return { processedCount, skippedCount };

        } catch (error) {
            this.logger.logError('CDR batch processing', error, {
                batchSize: cdrs.length
            });
            throw error;
        }
    }

    // Check if cleanup should run
    async shouldRunCleanup() {
        try {
            const lastCleanupTime = await this.stateClient.getLastCleanupTime();
            const timeSinceCleanup = Date.now() / 1000 - lastCleanupTime;
            const cleanupIntervalSeconds = config.processing.cleanupInterval / 1000;

            return timeSinceCleanup >= cleanupIntervalSeconds;
        } catch (error) {
            this.logger.warn('Warning: Error checking cleanup schedule, skipping cleanup:', { error: error.message });
            return false;
        }
    }

    // Sync processed CDRs from DuckDB to Supabase
    async syncToSupabase() {
        try {
            const startTime = Date.now();

            // Get unsynced charges from local DuckDB
            const unsyncedCharges = this.stateClient.getUnsyncedCharges(config.processing.batchSize);

            if (unsyncedCharges.length === 0) {
                this.logger.debug('No unsynced charges to sync to Supabase');
                return 0;
            }

            this.logger.info(`Syncing ${unsyncedCharges.length} unsynced charges to Supabase`);

            // Send to Supabase (simple append operation)
            const result = await this.supabaseClient.appendCharges(unsyncedCharges);

            if (result.successCount > 0) {
                // Mark successfully synced charges in DuckDB
                // Use successfulCdrIds if available (from individual insert), otherwise use slice method
                const syncedCdrIds = result.successfulCdrIds ||
                    unsyncedCharges
                        .slice(0, result.successCount)
                        .map(charge => charge.cdr_id);

                this.stateClient.markChargesAsSynced(syncedCdrIds);

                const duration = Date.now() - startTime;
                this.logger.info(`Supabase sync completed in ${duration}ms: ${result.successCount} synced, ${result.errorCount} errors`);
            } else {
                // All charges failed to sync - log the error
                const duration = Date.now() - startTime;
                this.logger.warn(`Supabase sync failed in ${duration}ms: ${result.errorCount} errors`);

                // Log first few errors for debugging
                if (result.errors && result.errors.length > 0) {
                    const firstError = result.errors[0];
                    this.logger.warn('First sync error:', { error: firstError.error || firstError });
                }
            }

            return result.successCount;

        } catch (error) {
            this.logger.warn('Warning: Supabase sync failed, will retry next cycle:', { error: error.message });
            return 0;
        }
    }

    // Run cleanup operation
    async runCleanup() {
        try {
            if (!(await this.shouldRunCleanup())) {
                return;
            }

            const startTime = Date.now();
            this.logger.info(`Starting cleanup of charges older than ${config.processing.rotationMonths} months`);

            // Cleanup both Supabase and local DuckDB
            const supabaseDeleted = await this.supabaseClient.cleanupOldCharges(config.processing.rotationMonths);
            const duckdbDeleted = this.stateClient.cleanupOldCDRs(config.processing.rotationMonths * 30); // Convert months to days

            const duration = Date.now() - startTime;
            this.logger.info(`Cleanup completed in ${duration}ms: Supabase: ${supabaseDeleted}, DuckDB: ${duckdbDeleted || 0} records deleted`);

        } catch (error) {
            this.logger.warn('Warning: Cleanup operation failed, will retry next cycle:', { error: error.message });
            // Don't throw - allow service to continue
        }
    }

    // Single processing cycle
    async processingCycle() {
        const cycleStartTime = Date.now();
        this.logger.logCycleStart();

        try {
            // Get last fetch time from local state
            const lastFetchTime = await this.stateClient.getLastFetchTime();

            // Fetch CDRs from MagnusBilling API
            const apiStartTime = Date.now();
            const response = await this.magnusClient.fetchCDRs(lastFetchTime, config.processing.batchSize);
            const apiDuration = Date.now() - apiStartTime;

            this.logger.logApiCall(config.magnus.apiUrl, apiDuration, response.cdrs.length);

            let totalProcessed = 0;
            let totalSkipped = 0;

            // Process CDRs sequentially to ensure idempotency
            if (response.cdrs.length > 0) {
                // Process in smaller batches for better memory management
                const chunks = this.chunkArray(response.cdrs, config.processing.batchSize);

                for (const chunk of chunks) {
                    try {
                        const result = await this.processCDRBatch(chunk);
                        totalProcessed += result.processedCount;
                        totalSkipped += result.skippedCount;
                    } catch (error) {
                        this.logger.warn('Warning: CDR batch processing failed, continuing with next batch:', {
                            error: error.message,
                            batchSize: chunk.length
                        });
                        totalSkipped += chunk.length; // Count all as skipped
                    }
                }

                // Update last fetch time only after processing attempts (even if some failed)
                try {
                    this.stateClient.updateLastFetchTime(response.fetchTime);
                } catch (error) {
                    this.logger.warn('Warning: Failed to update last fetch time:', { error: error.message });
                    // Continue - this will be retried next cycle
                }
            }

            // Sync processed CDRs to Supabase
            await this.syncToSupabase();

            // Run cleanup if needed (non-critical operation)
            await this.runCleanup();

            const cycleDuration = Date.now() - cycleStartTime;
            this.logger.logCycleComplete(cycleDuration, totalProcessed, totalSkipped);

            return { totalProcessed, totalSkipped };

        } catch (error) {
            this.logger.warn('Warning: Processing cycle encountered error:', { error: error.message });
            // Return partial results instead of throwing
            return { totalProcessed: 0, totalSkipped: 0 };
        }
    }

    // Main processing loop with sequential execution
    async start() {
        try {
            this.logger.logStartup(config);
            logConfig();

            // Cold start logic: Check if DuckDB file exists
            const duckDbExists = fs.existsSync(config.database.statePath);

            if (!duckDbExists) {
                // DuckDB file doesn't exist, check for cold_start.txt
                const coldStartPath = path.join(path.dirname(config.database.statePath), 'cold_start.txt');

                if (fs.existsSync(coldStartPath)) {
                    try {
                        // Read timestamp from cold_start.txt
                        const coldStartTimestamp = parseFloat(fs.readFileSync(coldStartPath, 'utf8').trim());

                        if (!isNaN(coldStartTimestamp) && coldStartTimestamp > 0) {
                            this.logger.info(`Cold start detected: Using timestamp ${coldStartTimestamp} (${new Date(coldStartTimestamp * 1000).toISOString()}) from cold_start.txt`);

                            // Initialize state management with cold start timestamp
                            this.stateClient.initializeWithTimestamp(coldStartTimestamp);
                        } else {
                            this.logger.warn('Invalid timestamp in cold_start.txt, falling back to default initialization');
                            this.stateClient.initialize();
                        }
                    } catch (error) {
                        this.logger.warn(`Error reading cold_start.txt: ${error.message}, falling back to default initialization`);
                        this.stateClient.initialize();
                    }
                } else {
                    this.logger.info('No DuckDB file and no cold_start.txt found, starting from beginning');
                    this.stateClient.initialize();
                }
            } else {
                // DuckDB file exists, use normal initialization
                this.logger.info('DuckDB file exists, using normal initialization');
                this.stateClient.initialize();
            }
            
            // Test Supabase connection
            const supabaseConnected = await this.supabaseClient.testConnection();
            if (!supabaseConnected) {
                this.logger.warn('Warning: Supabase connection test failed, but continuing startup. Service will retry connections during operation.');
                // Don't throw - allow service to start and retry connections during operation
            }

            this.logger.info('All systems initialized, starting main processing loop');

            // Setup metrics logging interval
            const metricsInterval = setInterval(() => {
                this.logger.logMetrics();
            }, config.logging.metricsInterval);

            // Main processing loop
            while (!this.shouldStop) {
                this.isRunning = true;
                
                // Processing cycle now handles its own errors and returns results
                await this.processingCycle();

                this.isRunning = false;

                // Sleep for configured interval
                if (!this.shouldStop) {
                    this.logger.debug(`Sleeping for ${config.processing.pollInterval}ms`);
                    await this.sleep(config.processing.pollInterval);
                }
            }

            // Cleanup
            clearInterval(metricsInterval);
            await this.stateClient.close();
            this.logger.logShutdown();

        } catch (error) {
            this.logger.logError('Service startup', error);
            process.exit(1);
        }
    }
}

// Main execution
async function main() {
    const processor = new CDRProcessor();
    await processor.start();
}

// Handle unhandled rejections
process.on('unhandledRejection', (reason, promise) => {
    console.error('Unhandled Rejection at:', promise, 'reason:', reason);
    process.exit(1);
});

process.on('uncaughtException', (error) => {
    console.error('Uncaught Exception:', error);
    process.exit(1);
});

// Start the service if this file is run directly
if (require.main === module) {
    main().catch(error => {
        console.error('Fatal error:', error);
        process.exit(1);
    });
}

module.exports = CDRProcessor;
