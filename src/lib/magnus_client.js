const crypto = require('crypto');

class MagnusBillingClient {
    constructor(apiUrl, apiKey, apiSecret) {
        this.apiUrl = apiUrl;
        this.apiKey = apiKey;
        this.apiSecret = apiSecret;
    }

    // Generate HMAC-SHA512 signature as required by MagnusBilling API
    generateSignature(postData) {
        return crypto.createHmac('sha512', this.apiSecret)
            .update(postData)
            .digest('hex');
    }

    // Sleep utility for retry delays
    async sleep(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }

    // Exponential backoff retry wrapper
    async withRetry(operation, maxRetries = 3, baseDelay = 10000) {
        let retryCount = 0;
        let lastError;

        while (retryCount <= maxRetries) {
            try {
                return await operation();
            } catch (error) {
                lastError = error;
                retryCount++;

                if (retryCount <= maxRetries) {
                    const delay = baseDelay * Math.pow(2, retryCount - 1); // 10s, 20s, 40s
                    console.warn(`Warning: API call failed (attempt ${retryCount}/${maxRetries + 1}), retrying in ${delay}ms:`, error.message);
                    await this.sleep(delay);
                } else {
                    console.error(`Error: API call failed after ${maxRetries + 1} attempts:`, error.message);
                }
            }
        }

        // Don't throw - return null to indicate failure and let caller handle it
        console.error('Error: All retry attempts exhausted, returning null');
        return null;
    }

    // Fetch CDRs from MagnusBilling API
    async fetchCDRs(sinceTimestamp = 0, limit = 1000) {
        const operation = async () => {
            const nonce = Math.floor(Date.now() / 1000);
            let postData = `action=read&module=Call&order=starttime+DESC&limit=${limit}&nonce=${nonce}`;

            // Add since parameter if provided (Unix timestamp)
            if (sinceTimestamp > 0) {
                postData += `&since=${Math.floor(sinceTimestamp)}`;
            }

            const signature = this.generateSignature(postData);

            console.log(`Fetching CDRs since ${new Date(sinceTimestamp * 1000).toISOString()}, limit: ${limit}`);

            const response = await fetch(this.apiUrl, {
                method: 'POST',
                headers: {
                    'KEY': this.apiKey,
                    'SIGN': signature,
                    'Content-Type': 'application/x-www-form-urlencoded'
                },
                body: postData
            });

            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }

            const data = await response.json();

            // Validate response structure
            if (!data || typeof data !== 'object') {
                throw new Error('Invalid response format: not a JSON object');
            }

            const cdrs = data.rows || [];
            console.log(`Fetched ${cdrs.length} CDRs from MagnusBilling API`);

            return {
                cdrs,
                count: data.count || 0,
                fetchTime: nonce
            };
        };

        const result = await this.withRetry(operation);

        // Handle retry failure gracefully
        if (result === null) {
            console.warn('Warning: Failed to fetch CDRs after all retries, returning empty result');
            return {
                cdrs: [],
                count: 0,
                fetchTime: Math.floor(Date.now() / 1000)
            };
        }

        return result;
    }

    // Validate CDR data according to requirements
    validateCDR(cdr) {
        const errors = [];

        // Ensure sessiontime > 0, id, src, starttime are present
        if (!cdr.id) {
            errors.push('Missing id');
        }
        
        if (!cdr.src) {
            errors.push('Missing src');
        }
        
        if (!cdr.starttime) {
            errors.push('Missing starttime');
        }
        
        const sessiontime = parseFloat(cdr.sessiontime);
        if (isNaN(sessiontime) || sessiontime <= 0) {
            errors.push('Invalid sessiontime (must be > 0)');
        }

        if (errors.length > 0) {
            console.warn(`Invalid CDR ${cdr.id}:`, errors.join(', '));
            return false;
        }

        return true;
    }

    // Process and clean CDR data
    processCDR(cdr) {
        if (!this.validateCDR(cdr)) {
            return null;
        }

        return {
            cdr_id: cdr.id,
            sip_user: cdr.src,
            duration: parseFloat(cdr.sessiontime),
            start_time: cdr.starttime,
            caller_id: cdr.callerid || '',
            called_station: cdr.calledstation || '',
            raw_data: cdr // Keep original data for debugging
        };
    }

    // Calculate charge based on duration and rate
    calculateCharge(duration, rate = 0.01) {
        // Default rate of 0.01 per second as shown in draft
        // This can be enhanced to support per-user rates from configuration
        return duration * rate;
    }

    // Calculate cost (actual cost to provider) based on CDR billing data
    calculateCost(cdr) {
        try {
            const buycost = parseFloat(cdr.buycost) || 0;

            // Cost is what we pay to the carrier/provider (buycost)
            return buycost;
        } catch (error) {
            console.warn(`Warning: Error calculating cost for CDR ${cdr.id}:`, error.message);
            return 0;
        }
    }

    // Calculate profit based on CDR billing data
    calculateProfit(cdr) {
        try {
            const buycost = parseFloat(cdr.buycost) || 0;
            const sessionbill = parseFloat(cdr.sessionbill) || 0;
            const agentBill = parseFloat(cdr.agent_bill) || 0;

            // Calculate profit: total revenue - cost to provider
            const totalRevenue = sessionbill + agentBill;
            const profit = totalRevenue - buycost;

            return profit;
        } catch (error) {
            console.warn(`Warning: Error calculating profit for CDR ${cdr.id}:`, error.message);
            return 0;
        }
    }

    // Calculate total revenue from CDR billing data
    calculateRevenue(cdr) {
        try {
            const sessionbill = parseFloat(cdr.sessionbill) || 0;
            const agentBill = parseFloat(cdr.agent_bill) || 0;

            return sessionbill + agentBill;
        } catch (error) {
            console.warn(`Warning: Error calculating revenue for CDR ${cdr.id}:`, error.message);
            return 0;
        }
    }
}

module.exports = MagnusBillingClient;
