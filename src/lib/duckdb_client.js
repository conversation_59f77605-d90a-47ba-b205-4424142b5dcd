const { open } = require('@evan/duckdb');
const fs = require('fs');
const path = require('path');

// DuckDB-based state management with ACID transactions
// High-performance embedded database for application state and CDR processing

class StateClient {
    constructor(dbPath = ':memory:') {
        this.dbPath = dbPath; // Use :memory: for in-memory DB or file path for persistent
        this.db = null;
        this.connection = null;
        this.initialized = false;
        this.defaultState = {
            last_fetch_time: 0,
            last_cleanup_time: 0,
            version: '1.0.0',
            created_at: new Date().toISOString()
        };
    }

    // Initialize DuckDB database and create state table
    initializeDatabase() {
        try {
            // Create database instance using @evan/duckdb
            this.db = open(this.dbPath);
            this.connection = this.db.connect();

            // Create state table with proper schema
            this.connection.query(`
                CREATE TABLE IF NOT EXISTS application_state (
                    key VARCHAR PRIMARY KEY,
                    value DOUBLE,
                    updated_at TIMESTAMP,
                    created_at TIMESTAMP
                )
            `);

            // Create CDR processing table for local operations
            this.connection.query(`
                CREATE TABLE IF NOT EXISTS cdr_processing (
                    cdr_id VARCHAR PRIMARY KEY,
                    sip_user VARCHAR NOT NULL,
                    duration DOUBLE NOT NULL,
                    charge DOUBLE NOT NULL,
                    cost DOUBLE DEFAULT 0,
                    start_time TIMESTAMP NOT NULL,
                    processed_at TIMESTAMP,
                    synced_to_supabase BOOLEAN DEFAULT FALSE,
                    raw_data VARCHAR
                )
            `);

            // Create indexes for better performance
            this.connection.query(`CREATE INDEX IF NOT EXISTS idx_state_key ON application_state(key)`);
            this.connection.query(`CREATE INDEX IF NOT EXISTS idx_cdr_id ON cdr_processing(cdr_id)`);
            this.connection.query(`CREATE INDEX IF NOT EXISTS idx_cdr_synced ON cdr_processing(synced_to_supabase)`);
            this.connection.query(`CREATE INDEX IF NOT EXISTS idx_cdr_processed_at ON cdr_processing(processed_at)`);

            console.log('DuckDB state management initialized successfully');
            return true;
        } catch (error) {
            console.error('Failed to initialize DuckDB:', error);
            throw error;
        }
    }

    // Execute SQL query with error handling using DuckDB
    executeQuery(sql, params = []) {
        try {
            if (params.length > 0) {
                // Use prepared statement for parameterized queries
                const prepared = this.connection.prepare(sql);
                return prepared.query(...params);
            } else {
                // Direct query for non-parameterized queries
                return this.connection.query(sql);
            }
        } catch (error) {
            console.error('DuckDB Error:', error.message, 'Query:', sql);
            throw error;
        }
    }

    // Execute query and return single value
    executeScalar(sql, params = []) {
        const result = this.executeQuery(sql, params);
        return result && result.length > 0 ? result[0] : null;
    }

    initialize() {
        if (this.initialized) return;

        try {
            // Initialize DuckDB database
            this.initializeDatabase();

            // Initialize default state values if they don't exist
            this.initializeDefaultState();

            this.initialized = true;
            console.log(`DuckDB state management initialized: ${this.dbPath}`);
        } catch (error) {
            console.error('Failed to initialize DuckDB state management:', error);
            throw error;
        }
    }

    // Initialize with a specific timestamp (for cold start recovery)
    initializeWithTimestamp(coldStartTimestamp) {
        if (this.initialized) return;

        try {
            // Initialize DuckDB database
            this.initializeDatabase();

            // Initialize state values with the provided cold start timestamp
            this.initializeStateWithTimestamp(coldStartTimestamp);

            this.initialized = true;
            console.log(`DuckDB state management initialized with cold start timestamp: ${coldStartTimestamp} (${new Date(coldStartTimestamp * 1000).toISOString()})`);
        } catch (error) {
            console.error('Failed to initialize DuckDB state management with timestamp:', error);
            throw error;
        }
    }

    // Initialize default state values
    initializeDefaultState() {
        try {
            // Check if state values exist, if not create them
            const existingFetchTime = this.getStateValue('last_fetch_time');
            if (existingFetchTime === null) {
                this.setStateValue('last_fetch_time', this.defaultState.last_fetch_time);
            }

            const existingCleanupTime = this.getStateValue('last_cleanup_time');
            if (existingCleanupTime === null) {
                this.setStateValue('last_cleanup_time', this.defaultState.last_cleanup_time);
            }

            console.log('Default state values initialized');
        } catch (error) {
            console.warn('Warning: Failed to initialize default state values:', error.message);
        }
    }

    // Initialize state values with a specific timestamp (for cold start recovery)
    initializeStateWithTimestamp(coldStartTimestamp) {
        try {
            // Set the last_fetch_time to the cold start timestamp
            this.setStateValue('last_fetch_time', coldStartTimestamp);

            // Initialize cleanup time with default value
            const existingCleanupTime = this.getStateValue('last_cleanup_time');
            if (existingCleanupTime === null) {
                this.setStateValue('last_cleanup_time', this.defaultState.last_cleanup_time);
            }

            console.log(`State values initialized with cold start timestamp: ${coldStartTimestamp} (${new Date(coldStartTimestamp * 1000).toISOString()})`);
        } catch (error) {
            console.warn('Warning: Failed to initialize state values with timestamp:', error.message);
        }
    }

    // Get state value by key
    getStateValue(key) {
        try {
            const result = this.executeScalar(
                'SELECT value FROM application_state WHERE key = ?',
                [key]
            );
            return result ? result.value : null;
        } catch (error) {
            console.warn(`Warning: Failed to get state value for ${key}:`, error.message);
            return null;
        }
    }

    // Set state value by key with automatic timestamp update
    setStateValue(key, value) {
        try {
            this.executeQuery(`
                INSERT INTO application_state (key, value, updated_at, created_at)
                VALUES (?, ?, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
                ON CONFLICT (key) DO UPDATE SET
                    value = EXCLUDED.value,
                    updated_at = CURRENT_TIMESTAMP
            `, [key, value]);

            console.log(`Updated state: ${key} = ${value}`);
        } catch (error) {
            console.error(`Error setting state value for ${key}:`, error.message);
            throw error;
        }
    }

    // Get all state values as object
    getAllState() {
        try {
            const results = this.executeQuery('SELECT key, value FROM application_state');
            const state = {};
            for (const row of results) {
                state[row.key] = row.value;
            }
            return state;
        } catch (error) {
            console.warn('Warning: Failed to get all state, using defaults:', error.message);
            return { ...this.defaultState };
        }
    }

    getLastFetchTime() {
        if (!this.initialized) {
            this.initialize();
        }

        try {
            const value = this.getStateValue('last_fetch_time');
            return value !== null ? value : 0;
        } catch (error) {
            console.warn('Warning: Error getting last fetch time, using default:', error.message);
            return 0;
        }
    }

    updateLastFetchTime(timestamp) {
        if (!this.initialized) {
            this.initialize();
        }

        try {
            const timestampFloat = typeof timestamp === 'number' ? timestamp : Date.now() / 1000;
            this.setStateValue('last_fetch_time', timestampFloat);
            console.log(`Updated last fetch time to: ${new Date(timestampFloat * 1000).toISOString()}`);
        } catch (error) {
            console.error('Error updating last fetch time:', error);
            throw error;
        }
    }

    getLastCleanupTime() {
        if (!this.initialized) {
            this.initialize();
        }

        try {
            const value = this.getStateValue('last_cleanup_time');
            return value !== null ? value : 0;
        } catch (error) {
            console.warn('Warning: Error getting last cleanup time, using default:', error.message);
            return 0;
        }
    }



    getState() {
        if (!this.initialized) {
            this.initialize();
        }

        try {
            return this.getAllState();
        } catch (error) {
            console.warn('Warning: Error getting state, using defaults:', error.message);
            return { last_fetch_time: 0, last_cleanup_time: 0 };
        }
    }

    // Get state statistics for monitoring
    getStateStats() {
        if (!this.initialized) {
            this.initialize();
        }

        try {
            const stats = this.executeQuery(`
                SELECT
                    key,
                    value,
                    updated_at,
                    created_at
                FROM application_state
                ORDER BY key
            `);

            return stats;
        } catch (error) {
            console.warn('Warning: Error getting state stats:', error.message);
            return [];
        }
    }

    // CDR Processing Methods

    // Check if CDRs already exist in local DuckDB (fast local query)
    checkExistingCDRs(cdrIds) {
        if (!cdrIds || cdrIds.length === 0) {
            return [];
        }

        try {
            // Use string interpolation instead of parameters for IN clause
            const quotedIds = cdrIds.map(id => `'${id}'`).join(',');
            const results = this.executeQuery(
                `SELECT cdr_id FROM cdr_processing WHERE cdr_id IN (${quotedIds})`
            );

            return results.map(row => row.cdr_id);
        } catch (error) {
            console.warn('Warning: Error checking existing CDRs in DuckDB:', error.message);
            return [];
        }
    }

    // Process CDR batch locally in DuckDB (ultra-fast)
    processCDRBatch(charges) {
        if (!charges || charges.length === 0) {
            return { successCount: 0, errorCount: 0, errors: [] };
        }

        const startTime = Date.now();
        console.log(`Processing ${charges.length} CDRs locally in DuckDB`);

        try {
            // Begin transaction for atomic batch processing
            this.executeQuery('BEGIN TRANSACTION');

            let successCount = 0;
            let errorCount = 0;
            const errors = [];

            for (const charge of charges) {
                try {
                    const now = new Date().toISOString();
                    this.executeQuery(`
                        INSERT INTO cdr_processing (
                            cdr_id, sip_user, duration, charge, cost, start_time, processed_at, raw_data
                        ) VALUES (?, ?, ?, ?, ?, '${charge.start_time}', '${now}', ?)
                        ON CONFLICT (cdr_id) DO NOTHING
                    `, [
                        charge.cdr_id,
                        charge.sip_user,
                        charge.duration,
                        charge.charge,
                        charge.cost,
                        JSON.stringify(charge.raw_data || {})
                    ]);

                    successCount++;
                } catch (error) {
                    errorCount++;
                    errors.push({
                        cdr_id: charge.cdr_id,
                        error: error.message
                    });
                }
            }

            // Commit transaction
            this.executeQuery('COMMIT');

            const duration = Date.now() - startTime;
            console.log(`DuckDB batch processing completed in ${duration}ms: ${successCount} successful, ${errorCount} errors`);

            return { successCount, errorCount, errors };

        } catch (error) {
            // Rollback on error
            this.executeQuery('ROLLBACK').catch(() => {});
            console.error('DuckDB batch processing failed:', error);
            throw error;
        }
    }

    // Get unsynced charges that need to be sent to Supabase
    getUnsyncedCharges(limit = 1000) {
        try {
            const results = this.executeQuery(`
                SELECT cdr_id, sip_user, duration, charge, cost, start_time
                FROM cdr_processing
                WHERE synced_to_supabase = 0
                ORDER BY processed_at ASC
                LIMIT ${limit}
            `);

            return results;
        } catch (error) {
            console.warn('Warning: Error getting unsynced charges:', error.message);
            return [];
        }
    }

    // Mark charges as synced to Supabase using DELETE + INSERT approach
    markChargesAsSynced(cdrIds) {
        if (!cdrIds || cdrIds.length === 0) {
            return;
        }

        try {
            // Use DELETE + INSERT approach to avoid UPDATE bug in DuckDB
            let successCount = 0;
            for (const cdrId of cdrIds) {
                try {
                    // First get the record data
                    const record = this.executeQuery(`
                        SELECT cdr_id, sip_user, duration, charge, cost, start_time, processed_at, raw_data
                        FROM cdr_processing
                        WHERE cdr_id = '${cdrId}'
                    `);

                    if (record && record.length > 0) {
                        const r = record[0];

                        // Delete the old record
                        this.executeQuery(`DELETE FROM cdr_processing WHERE cdr_id = '${cdrId}'`);

                        // Convert timestamps to proper ISO format for DuckDB
                        const convertTimestamp = (ts) => {
                            if (typeof ts === 'string' && /^\d+$/.test(ts)) {
                                // Unix timestamp in milliseconds
                                return new Date(parseInt(ts)).toISOString();
                            } else if (typeof ts === 'number') {
                                return new Date(ts).toISOString();
                            }
                            return ts; // Already in correct format
                        };

                        // Insert with synced_to_supabase = 1 using string interpolation
                        this.executeQuery(`
                            INSERT INTO cdr_processing (
                                cdr_id, sip_user, duration, charge, cost, start_time, processed_at, synced_to_supabase, raw_data
                            ) VALUES (
                                '${r.cdr_id}',
                                '${r.sip_user}',
                                ${r.duration},
                                ${r.charge},
                                ${r.cost},
                                '${convertTimestamp(r.start_time)}',
                                '${convertTimestamp(r.processed_at)}',
                                1,
                                '${r.raw_data}'
                            )
                        `);

                        successCount++;
                    } else {
                        console.warn(`Warning: CDR ${cdrId} not found in DuckDB for sync marking`);
                    }
                } catch (error) {
                    console.warn(`Warning: Failed to mark CDR ${cdrId} as synced:`, error.message);
                }
            }

            console.log(`Marked ${successCount}/${cdrIds.length} charges as synced to Supabase`);
        } catch (error) {
            console.error('Error marking charges as synced:', error);
            throw error;
        }
    }

    // Delete charges that are already in Supabase (duplicates)
    deleteDuplicateCharges(cdrIds) {
        if (!cdrIds || cdrIds.length === 0) {
            return;
        }

        try {
            // Use string interpolation instead of parameters for IN clause
            const quotedIds = cdrIds.map(id => `'${id}'`).join(',');
            this.executeQuery(`
                DELETE FROM cdr_processing
                WHERE cdr_id IN (${quotedIds})
            `);

            console.log(`Deleted ${cdrIds.length} duplicate charges from local DuckDB`);
        } catch (error) {
            console.error('Error deleting duplicate charges:', error);
            throw error;
        }
    }



    // Cleanup old processed CDRs (keep only recent ones)
    cleanupOldCDRs(daysToKeep = 90) {
        try {
            const cutoffDate = new Date();
            cutoffDate.setDate(cutoffDate.getDate() - daysToKeep);
            const cutoffTimestamp = cutoffDate.getTime(); // Unix timestamp in milliseconds

            // Only delete CDRs that are both old AND already synced to Supabase
            const result = this.executeQuery(`
                DELETE FROM cdr_processing
                WHERE processed_at < ${cutoffTimestamp} AND synced_to_supabase = 1
            `);

            console.log(`Cleaned up old CDRs older than ${daysToKeep} days`);

            // Create/update cold_start.txt with current last_fetch_time
            try {
                const lastFetchTime = this.getStateValue('last_fetch_time');
                if (lastFetchTime !== null && lastFetchTime > 0) {
                    const coldStartPath = path.join(path.dirname(this.dbPath), 'cold_start.txt');
                    fs.writeFileSync(coldStartPath, lastFetchTime.toString(), 'utf8');
                    console.log(`Updated cold_start.txt with timestamp: ${lastFetchTime} (${new Date(lastFetchTime * 1000).toISOString()})`);
                } else {
                    console.warn('Warning: Could not update cold_start.txt - invalid last_fetch_time');
                }
            } catch (coldStartError) {
                console.warn('Warning: Failed to update cold_start.txt:', coldStartError.message);
                // Don't throw - cleanup operation should still be considered successful
            }

            return { deletedCount: result?.changes || 0 };
        } catch (error) {
            console.warn('Warning: Error cleaning up old CDRs:', error.message);
            return { deletedCount: 0 };
        }
    }

    async close() {
        try {
            if (this.connection) {
                this.connection.close();
                this.connection = null;
            }
            if (this.db) {
                this.db.close();
                this.db = null;
            }
            console.log('DuckDB connection closed successfully');
        } catch (error) {
            console.warn('Warning: Error closing DuckDB connection:', error.message);
        }
        this.initialized = false;
    }
}

module.exports = StateClient;
