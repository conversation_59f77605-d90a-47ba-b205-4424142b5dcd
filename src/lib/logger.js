// Simple logger for Phantom Billing Service
// Uses console with structured logging format

class Logger {
    constructor(level = 'info') {
        this.level = level;
        this.levels = {
            error: 0,
            warn: 1,
            info: 2,
            debug: 3
        };
        this.currentLevel = this.levels[level] || this.levels.info;
        
        // Initialize metrics tracking
        this.metrics = {
            cdrsFetched: 0,
            cdrsProcessed: 0,
            cdrsSkipped: 0,
            errors: 0,
            apiCalls: 0,
            processingTime: 0,
            lastProcessingTime: 0,
            startTime: Date.now()
        };
    }

    formatMessage(level, message, meta = {}) {
        const timestamp = new Date().toISOString();
        const logObj = {
            timestamp,
            level: level.toUpperCase(),
            message,
            ...meta
        };
        return JSON.stringify(logObj);
    }

    log(level, message, meta = {}) {
        if (this.levels[level] <= this.currentLevel) {
            console.log(this.formatMessage(level, message, meta));
        }
    }

    error(message, meta = {}) {
        this.metrics.errors++;
        this.log('error', message, meta);
    }

    warn(message, meta = {}) {
        this.log('warn', message, meta);
    }

    info(message, meta = {}) {
        this.log('info', message, meta);
    }

    debug(message, meta = {}) {
        this.log('debug', message, meta);
    }

    // Update metrics
    updateMetrics(updates) {
        Object.assign(this.metrics, updates);
    }

    // Log processing metrics
    logMetrics() {
        const uptime = Date.now() - this.metrics.startTime;
        const avgProcessingTime = this.metrics.apiCalls > 0 ? 
            this.metrics.processingTime / this.metrics.apiCalls : 0;

        this.info('Processing metrics', {
            uptime: `${Math.floor(uptime / 1000)}s`,
            cdrsFetched: this.metrics.cdrsFetched,
            cdrsProcessed: this.metrics.cdrsProcessed,
            cdrsSkipped: this.metrics.cdrsSkipped,
            errors: this.metrics.errors,
            apiCalls: this.metrics.apiCalls,
            avgProcessingTime: `${Math.floor(avgProcessingTime)}ms`,
            lastProcessingTime: `${this.metrics.lastProcessingTime}ms`
        });
    }

    // Log processing cycle start
    logCycleStart() {
        this.info('Starting CDR processing cycle');
    }

    // Log processing cycle completion
    logCycleComplete(duration, processedCount, skippedCount) {
        this.metrics.lastProcessingTime = duration;
        this.metrics.processingTime += duration;
        this.metrics.cdrsProcessed += processedCount;
        this.metrics.cdrsSkipped += skippedCount;
        
        this.info('CDR processing cycle completed', {
            duration: `${duration}ms`,
            processed: processedCount,
            skipped: skippedCount
        });
    }

    // Log API call
    logApiCall(endpoint, duration, recordCount) {
        this.metrics.apiCalls++;
        this.metrics.cdrsFetched += recordCount;
        
        this.info('API call completed', {
            endpoint,
            duration: `${duration}ms`,
            recordCount
        });
    }

    // Log cleanup operation
    logCleanup(deletedCount, duration) {
        this.info('Cleanup operation completed', {
            deletedRecords: deletedCount,
            duration: `${duration}ms`
        });
    }

    // Log error with context
    logError(operation, error, context = {}) {
        this.error(`${operation} failed`, {
            error: error.message,
            stack: error.stack,
            ...context
        });
    }

    // Log startup
    logStartup(config) {
        this.info('Phantom Billing Service starting', {
            pollInterval: `${config.processing.pollInterval}ms`,
            batchSize: config.processing.batchSize,
            rotationMonths: config.processing.rotationMonths,
            defaultRate: config.processing.defaultRate
        });
    }

    // Log shutdown
    logShutdown() {
        this.info('Phantom Billing Service shutting down', {
            totalUptime: `${Math.floor((Date.now() - this.metrics.startTime) / 1000)}s`,
            totalCdrsProcessed: this.metrics.cdrsProcessed,
            totalErrors: this.metrics.errors
        });
    }
}

module.exports = Logger;
