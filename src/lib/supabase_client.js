const { createClient } = require('@supabase/supabase-js');

class SupabaseClient {
    constructor(supabaseUrl, supabaseKey) {
        this.client = createClient(supabaseUrl, supabaseKey);
    }

    // Check if CDR IDs already exist in call_charges table for deduplication
    async checkExistingCDRs(cdrIds) {
        const startTime = Date.now();
        try {
            if (!cdrIds || cdrIds.length === 0) {
                return [];
            }

            const { data, error } = await this.client
                .from('call_charges')
                .select('cdr_id')
                .in('cdr_id', cdrIds);

            if (error) {
                throw error;
            }

            const duration = Date.now() - startTime;
            console.log(`Deduplication check completed in ${duration}ms for ${cdrIds.length} CDRs, found ${data.length} existing`);

            return data.map(row => row.cdr_id);
        } catch (error) {
            const duration = Date.now() - startTime;
            console.warn(`Warning: Error checking existing CDRs after ${duration}ms, assuming none exist:`, error.message);
            // Return empty array to allow processing to continue
            return [];
        }
    }

    // Process charge with idempotency check
    async processCharge(cdrId, sipUser, duration, charge, cost, startTime) {
        try {
            // First check if the CDR already exists
            const { data: existing, error: checkError } = await this.client
                .from('call_charges')
                .select('cdr_id')
                .eq('cdr_id', cdrId)
                .maybeSingle();

            if (checkError) {
                console.warn(`Warning: Error checking existing CDR ${cdrId}:`, checkError.message);
                // Continue with insert attempt - duplicate key constraint will prevent duplicates
            }

            // If CDR already exists, return false (already processed)
            if (existing) {
                console.debug(`CDR ${cdrId} already exists, skipping`);
                return false;
            }

            // Insert the new charge record
            const { error: insertError } = await this.client
                .from('call_charges')
                .insert([
                    {
                        cdr_id: cdrId,
                        sip_user: sipUser,
                        duration: duration,
                        charge: charge,
                        cost: cost,
                        start_time: startTime
                    }
                ]);

            if (insertError) {
                // Check if it's a duplicate key error (expected in concurrent scenarios)
                if (insertError.code === '23505' || insertError.message.includes('duplicate')) {
                    console.debug(`CDR ${cdrId} already processed by another instance`);
                    return false;
                }
                console.error(`Error inserting charge for CDR ${cdrId}:`, insertError.message);
                return false; // Don't throw, just return failure
            }

            return true; // Successfully processed
        } catch (error) {
            console.warn(`Warning: Failed to process charge for CDR ${cdrId}:`, error.message);
            return false; // Don't throw, return failure to allow batch processing to continue
        }
    }

    // Execute operation within a transaction
    async withTransaction(operation) {
        // Note: Supabase doesn't expose direct transaction control in the client library
        // For now, we'll implement transaction-like behavior with rollback on error
        // In a production environment, consider using stored procedures for true ACID transactions

        const operationId = `txn_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
        console.log(`Starting transaction ${operationId}`);

        try {
            const result = await operation();
            console.log(`Transaction ${operationId} completed successfully`);
            return result;
        } catch (error) {
            console.error(`Transaction ${operationId} failed:`, error.message);
            throw error;
        }
    }

    // Optimized batch processing - single database query for deduplication
    async processChargesBatchOptimized(charges) {
        if (!charges || charges.length === 0) {
            return { successCount: 0, errorCount: 0, errors: [] };
        }

        const startTime = Date.now();
        console.log(`Processing batch of ${charges.length} charges`);

        return await this.withTransaction(async () => {
            try {
                // Extract CDR IDs to check for existing records (single query)
                const cdrIds = charges.map(charge => charge.cdr_id);
                const existingCdrIds = await this.checkExistingCDRs(cdrIds);
                const existingSet = new Set(existingCdrIds);

                // Filter out already processed charges
                const newCharges = charges.filter(charge => !existingSet.has(charge.cdr_id));
                const duplicateCount = charges.length - newCharges.length;

                if (duplicateCount > 0) {
                    console.log(`Skipping ${duplicateCount} duplicate charges`);
                }

                if (newCharges.length === 0) {
                    console.log('No new charges to process');
                    return {
                        successCount: 0,
                        errorCount: duplicateCount,
                        errors: [],
                        duplicates: duplicateCount
                    };
                }

                // Prepare records for bulk insert
                const records = newCharges.map(charge => ({
                    cdr_id: charge.cdr_id,
                    sip_user: charge.sip_user,
                    duration: charge.duration,
                    charge: charge.charge,
                    cost: charge.cost,
                    start_time: charge.start_time
                }));

                // Perform bulk insert
                const { error } = await this.client
                    .from('call_charges')
                    .insert(records);

                if (error) {
                    console.error('Batch insert error:', error);
                    throw error;
                }

                const successCount = newCharges.length;
                const duration = Date.now() - startTime;
                console.log(`Batch processing completed in ${duration}ms: ${successCount} successful, ${duplicateCount} duplicates`);

                return {
                    successCount,
                    errorCount: 0,
                    errors: [],
                    duplicates: duplicateCount
                };
            } catch (error) {
                console.error('Batch processing failed:', error);

                // Fall back to individual processing if bulk insert fails
                console.log('Falling back to individual charge processing...');
                return await this.processChargesBatchIndividual(charges);
            }
        });
    }

    // Ultra-fast batch insert without deduplication (use when CDRs are known to be new)
    async processChargesBatchFast(charges) {
        if (!charges || charges.length === 0) {
            return { successCount: 0, errorCount: 0, errors: [] };
        }

        const startTime = Date.now();
        console.log(`Fast processing batch of ${charges.length} charges (no deduplication)`);

        try {
            // Prepare records for bulk insert
            const records = charges.map(charge => ({
                cdr_id: charge.cdr_id,
                sip_user: charge.sip_user,
                duration: charge.duration,
                charge: charge.charge,
                cost: charge.cost,
                start_time: charge.start_time
            }));

            // Perform bulk insert without deduplication check
            const { error } = await this.client
                .from('call_charges')
                .insert(records);

            if (error) {
                // If it's duplicate key errors, that's expected - count them as skipped
                if (error.code === '23505' || error.message.includes('duplicate')) {
                    console.log('Some duplicates found during fast insert, falling back to safe method');
                    return await this.processChargesBatchOptimized(charges);
                }
                throw error;
            }

            const duration = Date.now() - startTime;
            console.log(`Fast batch processing completed in ${duration}ms: ${charges.length} successful`);

            return {
                successCount: charges.length,
                errorCount: 0,
                errors: [],
                duplicates: 0
            };
        } catch (error) {
            console.warn('Fast batch processing failed, falling back to safe method:', error.message);
            return await this.processChargesBatchOptimized(charges);
        }
    }

    // Smart batch processing - tries fast method first, falls back to safe method
    async processChargesBatchSmart(charges) {
        if (!charges || charges.length === 0) {
            return { successCount: 0, errorCount: 0, errors: [] };
        }

        // For small batches or when we expect few duplicates, try fast method first
        if (charges.length <= 100) {
            console.log('Using fast processing for small batch');
            return await this.processChargesBatchFast(charges);
        } else {
            // For larger batches, use safe method with deduplication
            console.log('Using safe processing for large batch');
            return await this.processChargesBatchOptimized(charges);
        }
    }

    // Simple append charges to Supabase (no deduplication - DuckDB handles that)
    async appendCharges(charges) {
        if (!charges || charges.length === 0) {
            return { successCount: 0, errorCount: 0, errors: [] };
        }

        const startTime = Date.now();
        console.log(`Appending ${charges.length} charges to Supabase (no deduplication)`);

        try {
            // Prepare records for simple insert with proper timestamp conversion
            const records = charges.map(charge => {
                // Convert start_time to proper ISO string if it's a Unix timestamp
                let startTime = charge.start_time;
                if (typeof startTime === 'number') {
                    // If it's a Unix timestamp in milliseconds
                    startTime = new Date(startTime).toISOString();
                } else if (typeof startTime === 'string' && /^\d+$/.test(startTime)) {
                    // If it's a string containing only digits (Unix timestamp)
                    const timestamp = parseInt(startTime);
                    startTime = new Date(timestamp * (timestamp > 9999999999 ? 1 : 1000)).toISOString();
                }

                return {
                    cdr_id: charge.cdr_id,
                    sip_user: charge.sip_user,
                    duration: charge.duration,
                    charge: charge.charge,
                    cost: charge.cost,
                    start_time: startTime
                };
            });

            // Simple bulk insert (no deduplication checks)
            const { error } = await this.client
                .from('call_charges')
                .insert(records);

            if (error) {
                console.error('Supabase append error:', error);

                // If it's a duplicate key error, try individual inserts to identify which ones are actually duplicates
                if (error.code === '23505' || (error.message && error.message.includes('duplicate key'))) {
                    console.log('Bulk insert failed due to duplicates, trying individual inserts...');
                    return await this.insertChargesIndividually(records);
                }

                // For other errors, return failure
                return {
                    successCount: 0,
                    errorCount: charges.length,
                    errors: [{ error: error.message || JSON.stringify(error) }]
                };
            }

            const duration = Date.now() - startTime;
            console.log(`Supabase append completed in ${duration}ms: ${charges.length} charges added`);

            return {
                successCount: charges.length,
                errorCount: 0,
                errors: []
            };
        } catch (error) {
            console.error('Supabase append failed:', error);
            return {
                successCount: 0,
                errorCount: charges.length,
                errors: [{ error: error.message }]
            };
        }
    }

    // Insert charges individually to handle partial duplicates
    async insertChargesIndividually(records) {
        let successCount = 0;
        let errorCount = 0;
        const errors = [];
        const successfulCdrIds = [];

        console.log(`Attempting individual insert for ${records.length} records...`);

        for (const record of records) {
            try {
                const { error } = await this.client
                    .from('call_charges')
                    .insert([record]);

                if (error) {
                    if (error.code === '23505' || (error.message && error.message.includes('duplicate key'))) {
                        // This specific record is a duplicate - that's expected, count as success
                        successCount++;
                        successfulCdrIds.push(record.cdr_id);
                        console.log(`CDR ${record.cdr_id} already exists in Supabase (duplicate)`);
                    } else {
                        // This is a real error
                        errorCount++;
                        errors.push({
                            cdr_id: record.cdr_id,
                            error: error.message || JSON.stringify(error)
                        });
                        console.warn(`Failed to insert CDR ${record.cdr_id}:`, error.message);
                    }
                } else {
                    // Successfully inserted new record
                    successCount++;
                    successfulCdrIds.push(record.cdr_id);
                    console.log(`CDR ${record.cdr_id} successfully inserted`);
                }
            } catch (insertError) {
                errorCount++;
                errors.push({
                    cdr_id: record.cdr_id,
                    error: insertError.message
                });
                console.warn(`Exception inserting CDR ${record.cdr_id}:`, insertError.message);
            }
        }

        console.log(`Individual insert completed: ${successCount} successful, ${errorCount} errors`);

        return {
            successCount,
            errorCount,
            errors,
            successfulCdrIds // Include the list of successful CDR IDs
        };
    }

    // Legacy batch process method (kept for compatibility)
    async processChargesBatch(charges) {
        // Redirect to smart version
        return await this.processChargesBatchSmart(charges);
    }

    // Fallback method for individual processing when bulk insert fails
    async processChargesBatchIndividual(charges) {
        let successCount = 0;
        let errorCount = 0;
        const errors = [];

        for (const charge of charges) {
            const success = await this.processCharge(
                charge.cdr_id,
                charge.sip_user,
                charge.duration,
                charge.charge,
                charge.cost,
                charge.start_time
            );

            if (success) {
                successCount++;
            } else {
                errorCount++;
                // processCharge already logs the reason for failure
            }
        }

        console.log(`Individual batch processing completed: ${successCount} successful, ${errorCount} errors/duplicates`);

        return {
            successCount,
            errorCount,
            errors
        };
    }


    // Clean up old charges using direct delete operation
    async cleanupOldCharges(monthsToKeep = 3) {
        try {
            console.log(`Starting cleanup of charges older than ${monthsToKeep} months`);

            // Calculate the cutoff date
            const cutoffDate = new Date();
            cutoffDate.setMonth(cutoffDate.getMonth() - monthsToKeep);
            const cutoffISOString = cutoffDate.toISOString();

            // Delete old records based on start_time (when the call occurred)
            const { error, count } = await this.client
                .from('call_charges')
                .delete({ count: 'exact' })
                .lt('start_time', cutoffISOString);

            if (error) {
                console.warn('Warning: Error during cleanup operation:', error.message);
                return 0; // Return 0 instead of throwing to allow service to continue
            }

            const deletedCount = count || 0;
            console.log(`Cleanup completed: ${deletedCount} old charges deleted`);

            return deletedCount;
        } catch (error) {
            console.warn('Warning: Cleanup operation failed:', error.message);
            return 0; // Return 0 instead of throwing to allow service to continue
        }
    }

    // Get statistics for monitoring
    async getStats() {
        try {
            // Get total charges count
            const { count: totalCharges, error: chargeError } = await this.client
                .from('call_charges')
                .select('*', { count: 'exact', head: true });

            if (chargeError) {
                throw chargeError;
            }

            // Get unique users count from charges
            const { data: uniqueUsers, error: userError } = await this.client
                .from('call_charges')
                .select('sip_user')
                .neq('sip_user', null);

            if (userError) {
                throw userError;
            }

            // Get recent charges (last 24 hours)
            const yesterday = new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString();
            const { count: recentCharges, error: recentError } = await this.client
                .from('call_charges')
                .select('*', { count: 'exact', head: true })
                .gte('created_at', yesterday);

            if (recentError) {
                throw recentError;
            }

            const uniqueUserCount = uniqueUsers ? new Set(uniqueUsers.map(u => u.sip_user)).size : 0;

            return {
                totalCharges: totalCharges || 0,
                totalUsers: uniqueUserCount,
                recentCharges: recentCharges || 0
            };
        } catch (error) {
            console.error('Error getting stats:', error);
            return {
                totalCharges: 0,
                totalUsers: 0,
                recentCharges: 0
            };
        }
    }



    // Test connection to Supabase
    async testConnection() {
        try {
            const { error } = await this.client
                .from('call_charges')
                .select('cdr_id')
                .limit(1);

            if (error) {
                console.warn('Warning: Supabase connection test failed:', error.message);
                return false;
            }

            console.log('Supabase connection test successful');
            return true;
        } catch (error) {
            console.warn('Warning: Supabase connection test failed:', error.message);
            return false;
        }
    }
}

module.exports = SupabaseClient;
