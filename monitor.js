#!/usr/bin/env bun

// Phantom Billing Service Monitor
// Simple monitoring script to check service health and display metrics

const fs = require('fs').promises;
const { config } = require('./src/config.js');

class ServiceMonitor {
    constructor() {
        this.statePath = config.database.statePath || ':memory:';
        this.stateClient = null;
    }

    async getSystemInfo() {
        
        // Basic system checks
        const checks = {
            timestamp: new Date().toISOString(),
            pid: null,
            memory: null,
            cpu: null,
            state: null,
            lastActivity: null,
            errors: []
        };

        try {
            // Check if service is running via PM2
            const { execSync } = require('child_process');
            try {
                const pm2Status = execSync('pm2 jlist', { encoding: 'utf8' });
                const processes = JSON.parse(pm2Status);
                const billingService = processes.find(p => p.name === 'phantom-billing-service');
                
                if (billingService) {
                    checks.pid = billingService.pid;
                    checks.status = billingService.pm2_env.status;
                    checks.restarts = billingService.pm2_env.restart_time;
                    checks.uptime = billingService.pm2_env.pm_uptime;
                } else {
                    checks.errors.push('Service not found in PM2 process list');
                }
            } catch (error) {
                checks.errors.push('Could not get PM2 status (is PM2 installed?)');
            }

            // Get memory usage if process exists
            if (checks.pid) {
                try {
                    const memResult = execSync(`ps -p ${checks.pid} -o rss=`, { encoding: 'utf8' });
                    checks.memory = `${Math.round(parseInt(memResult.trim()) / 1024)}MB`;
                } catch (error) {
                    checks.errors.push('Could not get memory usage');
                }
            }

            // Read state from DuckDB
            try {
                if (!this.stateClient) {
                    const StateClient = require('./src/lib/duckdb_client.js');
                    this.stateClient = new StateClient(this.statePath);
                    await this.stateClient.initialize();
                }

                const state = await this.stateClient.getAllState();
                const stats = await this.stateClient.getStateStats();

                checks.state = state;

                // Calculate last activity from state stats
                if (stats && stats.length > 0) {
                    const latestUpdate = stats.reduce((latest, row) => {
                        const updateTime = new Date(row.updated_at);
                        return updateTime > latest ? updateTime : latest;
                    }, new Date(0));

                    const timeSince = Date.now() - latestUpdate.getTime();
                    checks.lastActivity = `${Math.floor(timeSince / 1000)}s ago`;
                } else {
                    checks.lastActivity = 'No activity recorded';
                }
            } catch (error) {
                checks.errors.push(`Could not read DuckDB state: ${error.message}`);
            }

        } catch (error) {
            checks.errors.push(`System check failed: ${error.message}`);
        }

        return checks;
    }

    async getLogStats() {
        const logPaths = [
            './logs/combined.log',
            './logs/out.log',
            './logs/error.log'
        ];

        for (const logPath of logPaths) {
            try {
                const { execSync } = require('child_process');
                
                // Get recent error count
                const errorCount = execSync(`grep -c "ERROR" ${logPath} 2>/dev/null || echo 0`, { encoding: 'utf8' });
                
                // Get recent processing metrics
                const metricsOutput = execSync(`grep "Processing metrics" ${logPath} | tail -1 2>/dev/null || echo ""`, { encoding: 'utf8' });
                
                return {
                    logPath,
                    recentErrors: parseInt(errorCount.trim()),
                    lastMetrics: metricsOutput.trim()
                };
            } catch (error) {
                // Try next log path
                continue;
            }
        }

        return {
            logPath: 'none found',
            recentErrors: 0,
            lastMetrics: 'No metrics available'
        };
    }

    formatUptime(seconds) {
        const days = Math.floor(seconds / 86400);
        const hours = Math.floor((seconds % 86400) / 3600);
        const minutes = Math.floor((seconds % 3600) / 60);
        const secs = seconds % 60;

        if (days > 0) {
            return `${days}d ${hours}h ${minutes}m`;
        } else if (hours > 0) {
            return `${hours}h ${minutes}m ${secs}s`;
        } else if (minutes > 0) {
            return `${minutes}m ${secs}s`;
        } else {
            return `${secs}s`;
        }
    }

    async displayStatus() {
        console.log('=== Phantom Billing Service Monitor ===\\n');

        const info = await this.getSystemInfo();
        const logStats = await this.getLogStats();

        // Service Status
        console.log('🔍 Service Status:');
        if (info.pid) {
            console.log(`  ✅ Running (PID: ${info.pid})`);
            if (info.status) {
                console.log(`  📊 Status: ${info.status}`);
            }
            if (info.restarts !== undefined) {
                console.log(`  🔄 Restarts: ${info.restarts}`);
            }
            if (info.uptime) {
                const uptimeMs = Date.now() - info.uptime;
                const uptimeStr = this.formatUptime(Math.floor(uptimeMs / 1000));
                console.log(`  ⏱️  Uptime: ${uptimeStr}`);
            }
            if (info.memory) {
                console.log(`  💾 Memory: ${info.memory}`);
            }
        } else {
            console.log('  ❌ Not running');
        }

        if (info.errors.length > 0) {
            console.log('  ⚠️  Issues:');
            info.errors.forEach(error => console.log(`     - ${error}`));
        }

        console.log('');

        // State Information
        console.log('📊 State Information:');
        if (info.state) {
            const lastFetch = new Date((info.state.last_fetch_time || 0) * 1000);
            const lastCleanup = new Date((info.state.last_cleanup_time || 0) * 1000);

            console.log(`  📅 Last Fetch: ${lastFetch.toISOString()}`);
            console.log(`  🧹 Last Cleanup: ${lastCleanup.toISOString()}`);
            console.log(`  ⏱️  Last Activity: ${info.lastActivity}`);
            console.log(`  🦆 Database: ${this.statePath === ':memory:' ? 'In-Memory DuckDB' : `DuckDB: ${this.statePath}`}`);
        } else {
            console.log('  ❌ DuckDB state not available');
        }

        console.log('');

        // Log Statistics
        console.log('📋 Log Statistics:');
        console.log(`  📁 Log File: ${logStats.logPath}`);
        console.log(`  ❌ Recent Errors: ${logStats.recentErrors}`);
        if (logStats.lastMetrics) {
            console.log(`  📈 Last Metrics: ${logStats.lastMetrics}`);
        }

        console.log('');

        // Configuration Summary
        console.log('⚙️  Configuration:');
        console.log(`  🔄 Poll Interval: ${config.processing.pollInterval / 1000}s`);
        console.log(`  📦 Batch Size: ${config.processing.batchSize}`);
        console.log(`  🗑️  Rotation: ${config.processing.rotationMonths} months`);
        console.log(`  💰 Default Rate: $${config.processing.defaultRate}/second`);

        console.log('');

        // Quick Actions
        console.log('🛠️  Quick Actions:');
        console.log('  View logs:     pm2 logs phantom-billing-service');
        console.log('  Service status: pm2 status');
        console.log('  Restart:       pm2 restart phantom-billing-service');
        console.log('  Stop:          pm2 stop phantom-billing-service');
        console.log('  Start:         pm2 start ecosystem.config.js');
        console.log('  Monitor:       pm2 monit');
        console.log('  Delete:        pm2 delete phantom-billing-service');
    }

    async watchLogs() {
        console.log('Watching logs (Ctrl+C to exit)...');
        const { spawn } = require('child_process');
        
        const logPaths = [
            './logs/combined.log',
            './logs/out.log'
        ];

        for (const logPath of logPaths) {
            try {
                await fs.access(logPath);
                const tail = spawn('tail', ['-f', logPath]);
                
                tail.stdout.on('data', (data) => {
                    process.stdout.write(data);
                });

                tail.stderr.on('data', (data) => {
                    process.stderr.write(data);
                });

                tail.on('close', (code) => {
                    console.log(`Log monitoring stopped (exit code: ${code})`);
                });

                return; // Found and started monitoring
            } catch (error) {
                continue; // Try next path
            }
        }

        console.log('No log files found to monitor');
    }
}

async function main() {
    const monitor = new ServiceMonitor();
    
    const command = process.argv[2] || 'status';
    
    switch (command) {
        case 'status':
        case 'check':
            await monitor.displayStatus();
            break;
            
        case 'logs':
        case 'watch':
            await monitor.watchLogs();
            break;
            
        case 'help':
            console.log('Phantom Billing Service Monitor');
            console.log('');
            console.log('Usage: bun monitor.js [command]');
            console.log('');
            console.log('Commands:');
            console.log('  status    Show service status and metrics (default)');
            console.log('  logs      Watch live logs');
            console.log('  help      Show this help message');
            break;
            
        default:
            console.log(`Unknown command: ${command}`);
            console.log('Run "bun monitor.js help" for usage information');
            process.exit(1);
    }
}

// Run if executed directly
if (require.main === module) {
    main().catch(error => {
        console.error('Monitor error:', error);
        process.exit(1);
    });
}

module.exports = ServiceMonitor;
