# dependencies (bun install)
node_modules

# output
out
build
*.tgz

# code coverage
coverage
*.lcov

# logs
logs
_.log
report.[0-9]_.[0-9]_.[0-9]_.[0-9]_.json

# dotenv environment variable files
.env
.env.development.local
.env.test.local
.env.production.local
.env.local

# caches
.eslintcache
.cache
*.tsbuildinfo

# IntelliJ based IDEs
.idea

# Finder (MacOS) folder config
.DS_Store

# Application state files (DuckDB)
*.duckdb
*.duckdb.wal
*.backup
state.json.lock
phantom_billing.duckdb

# Log files
*.log
logs/*
!logs/.gitkeep

# PM2 files
.pm2/
